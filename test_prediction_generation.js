// 测试19:58预测生成

// 模拟预测服务的核心逻辑
class TestPredictionService {
  // 检查是否为有效的预测触发时间
  isValidPredictionTime(hour, minute) {
    const isOddHour = hour % 2 === 1;
    const paddedMinute = minute.toString().padStart(2, '0');
    
    if (isOddHour) {
      return ['02', '28', '58'].includes(paddedMinute);
    } else {
      return ['28'].includes(paddedMinute);
    }
  }

  // 计算预测所需的时间参数（修复后的版本）
  calculateTimes(currentTime) {
    const hour = currentTime.getUTCHours();
    const minute = currentTime.getUTCMinutes();
    const isOddHour = hour % 2 === 1;

    let sourceKlineTime;
    let targetStartTime;
    let targetEndTime;

    let workDate = new Date(currentTime);
    workDate.setUTCSeconds(0);
    workDate.setUTCMilliseconds(0);

    if (isOddHour) {
      if (minute === 2) {
        workDate.setUTCMinutes(0);
        sourceKlineTime = workDate.getTime();
        targetStartTime = sourceKlineTime;
        
        workDate = new Date(currentTime);
        workDate.setUTCSeconds(0);
        workDate.setUTCMilliseconds(0);
        workDate.setUTCMinutes(30);
        targetEndTime = workDate.getTime();
      } else if (minute === 28) {
        workDate.setUTCMinutes(1);
        sourceKlineTime = workDate.getTime();
        
        workDate = new Date(currentTime);
        workDate.setUTCSeconds(0);
        workDate.setUTCMilliseconds(0);
        workDate.setUTCMinutes(30);
        targetStartTime = workDate.getTime();
        
        workDate = new Date(currentTime);
        workDate.setUTCSeconds(0);
        workDate.setUTCMilliseconds(0);
        workDate.setUTCHours(hour + 1);
        workDate.setUTCMinutes(0);
        targetEndTime = workDate.getTime();
      } else if (minute === 58) {
        workDate.setUTCMinutes(2);
        sourceKlineTime = workDate.getTime();
        
        workDate = new Date(currentTime);
        workDate.setUTCSeconds(0);
        workDate.setUTCMilliseconds(0);
        workDate.setUTCHours(hour + 1);
        workDate.setUTCMinutes(0);
        targetStartTime = workDate.getTime();
        
        workDate = new Date(currentTime);
        workDate.setUTCSeconds(0);
        workDate.setUTCMilliseconds(0);
        workDate.setUTCHours(hour + 1);
        workDate.setUTCMinutes(30);
        targetEndTime = workDate.getTime();
      }
    } else {
      if (minute === 28) {
        if (hour === 0) {
          const prevDay = new Date(workDate.getTime());
          prevDay.setUTCDate(prevDay.getUTCDate() - 1);
          prevDay.setUTCHours(23);
          prevDay.setUTCMinutes(3);
          sourceKlineTime = prevDay.getTime();
        } else {
          workDate.setUTCHours(hour - 1);
          workDate.setUTCMinutes(3);
          sourceKlineTime = workDate.getTime();
        }
        
        workDate = new Date(currentTime);
        workDate.setUTCSeconds(0);
        workDate.setUTCMilliseconds(0);
        workDate.setUTCMinutes(30);
        targetStartTime = workDate.getTime();
        
        workDate.setUTCHours(hour + 1);
        workDate.setUTCMinutes(0);
        targetEndTime = workDate.getTime();
      }
    }

    return { sourceKlineTime, targetStartTime, targetEndTime };
  }

  // 模拟预测生成过程
  async simulateGeneratePrediction(currentTime, symbol = 'BTCUSDT') {
    try {
      const hour = currentTime.getUTCHours();
      const minute = currentTime.getUTCMinutes();
      
      console.log(`[测试预测服务][${currentTime.toISOString()}] 开始执行预测任务，小时: ${hour}, 分钟: ${minute}`);
      
      // 验证是否为指定的预测触发时间
      if (!this.isValidPredictionTime(hour, minute)) {
        console.log(`[测试预测服务] 不是有效的预测时间: ${hour}:${minute}`);
        return null;
      }

      // 获取预测的目标时间段和源K线时间
      const { sourceKlineTime, targetStartTime, targetEndTime } = this.calculateTimes(currentTime);
      
      console.log(`[测试预测服务] 计算出的时间参数:`);
      console.log(`  源K线时间: ${new Date(sourceKlineTime).toISOString()}`);
      console.log(`  目标开始: ${new Date(targetStartTime).toISOString()}`);
      console.log(`  目标结束: ${new Date(targetEndTime).toISOString()}`);
      
      // 模拟检查数据库中是否已存在相同时间段的预测
      console.log(`[测试预测服务] 检查数据库中是否存在目标时间段: ${new Date(targetStartTime).toISOString()}`);
      
      // 模拟从币安API获取K线数据
      console.log(`[测试预测服务] 模拟获取源K线数据: ${new Date(sourceKlineTime).toISOString()}`);
      
      // 模拟生成预测K线
      const mockPrediction = {
        symbol,
        predictionTime: currentTime.getTime(),
        targetStartTime,
        targetEndTime,
        sourceKlineTime,
        open: '118400.00',
        high: '118500.00',
        low: '118300.00',
        close: '118450.00',
        isActive: true
      };
      
      console.log(`[测试预测服务] 成功生成预测，目标时间段: ${new Date(targetStartTime).toISOString()} - ${new Date(targetEndTime).toISOString()}`);
      
      return mockPrediction;
    } catch (error) {
      console.error('[测试预测服务] 生成预测时出错:', error);
      return null;
    }
  }
}

// 测试函数
async function testPredictionGeneration() {
  console.log('=== 测试19:58预测生成 ===\n');
  
  const testService = new TestPredictionService();
  
  // 测试不同的时间点
  const testTimes = [
    new Date('2025-07-23T19:28:00.000Z'), // 19:28 - 应该成功
    new Date('2025-07-23T19:58:00.000Z'), // 19:58 - 关键测试点
    new Date('2025-07-23T20:28:00.000Z'), // 20:28 - 应该成功
    new Date('2025-07-23T19:57:00.000Z'), // 19:57 - 应该跳过
    new Date('2025-07-23T19:59:00.000Z'), // 19:59 - 应该跳过
  ];
  
  for (const testTime of testTimes) {
    console.log(`\n--- 测试时间: ${testTime.toISOString()} ---`);
    const result = await testService.simulateGeneratePrediction(testTime);
    
    if (result) {
      console.log('✅ 预测生成成功');
    } else {
      console.log('❌ 预测生成失败或跳过');
    }
    console.log('---');
  }
  
  // 特别测试19:58的详细情况
  console.log('\n=== 19:58详细测试 ===');
  const test1958 = new Date('2025-07-23T19:58:00.000Z');
  
  console.log(`测试时间: ${test1958.toISOString()}`);
  console.log(`UTC小时: ${test1958.getUTCHours()}`);
  console.log(`UTC分钟: ${test1958.getUTCMinutes()}`);
  console.log(`是否奇数小时: ${test1958.getUTCHours() % 2 === 1}`);
  
  const isValid = testService.isValidPredictionTime(test1958.getUTCHours(), test1958.getUTCMinutes());
  console.log(`时间验证结果: ${isValid}`);
  
  if (isValid) {
    const times = testService.calculateTimes(test1958);
    console.log('\n时间计算结果:');
    console.log(`源K线时间: ${new Date(times.sourceKlineTime).toISOString()}`);
    console.log(`目标开始时间: ${new Date(times.targetStartTime).toISOString()}`);
    console.log(`目标结束时间: ${new Date(times.targetEndTime).toISOString()}`);
    
    // 验证目标时间段是否正确
    const expectedStart = new Date('2025-07-23T20:00:00.000Z');
    const expectedEnd = new Date('2025-07-23T20:30:00.000Z');
    
    console.log('\n预期结果验证:');
    console.log(`目标开始时间正确: ${times.targetStartTime === expectedStart.getTime() ? '✅' : '❌'}`);
    console.log(`目标结束时间正确: ${times.targetEndTime === expectedEnd.getTime() ? '✅' : '❌'}`);
  }
}

// 运行测试
testPredictionGeneration().catch(console.error);
