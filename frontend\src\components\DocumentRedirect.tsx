import React, { useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

interface DocumentRedirectProps {
  documentIds: {
    zh: string;  // 中文文档ID
    en: string;  // 英文文档ID
  };
}

const DocumentRedirect: React.FC<DocumentRedirectProps> = ({ documentIds }) => {
  const { i18n } = useTranslation();

  // 根据当前语言选择对应的文档ID
  const getDocumentId = () => {
    return i18n.language === 'zh' ? documentIds.zh : documentIds.en;
  };

  const documentId = getDocumentId();
  const redirectPath = `/faq/article/${documentId}`;

  // 在控制台记录重定向信息（用于调试）
  useEffect(() => {
    console.log(`[DocumentRedirect] 语言: ${i18n.language}, 重定向到: ${redirectPath}`);
  }, [i18n.language, redirectPath]);

  return <Navigate to={redirectPath} replace />;
};

export default DocumentRedirect;
