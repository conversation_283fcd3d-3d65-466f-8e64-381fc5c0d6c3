const mongoose = require('mongoose');

// 直接定义Document模型
const documentSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  content: {
    type: String,
    required: true
  },
  category: {
    type: String,
    enum: ['guide', 'faq', 'announcement'],
    required: true,
    index: true
  },
  language: {
    type: String,
    enum: ['zh', 'en'],
    required: true,
    index: true
  },
  isVisible: {
    type: Boolean,
    default: true,
    index: true
  },
  order: {
    type: Number,
    default: 0,
    index: true
  },
  originalId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Document',
    default: null
  }
}, {
  timestamps: true
});

const Document = mongoose.model('Document', documentSchema);

// 连接数据库
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/btc-prediction');
    console.log('MongoDB 连接成功');
  } catch (error) {
    console.error('MongoDB 连接失败:', error);
    process.exit(1);
  }
};

// 测试语言切换功能
const testLanguageSwitching = async () => {
  try {
    console.log('测试语言切换功能...\n');

    // 文档标题映射关系
    const titleMappings = {
      'terms': { zh: '服务条款', en: 'Terms of Service' },
      'privacy': { zh: '隐私政策', en: 'Privacy Policy' },
      'prediction': { zh: '如何理解预测数据？', en: 'How to Understand Prediction Data?' },
      'quickstart': { zh: '快速开始指南', en: 'Quick Start Guide' },
    };

    console.log('🔄 语言切换测试场景：');
    console.log('=====================================');

    for (const [key, mapping] of Object.entries(titleMappings)) {
      console.log(`\n📄 测试文档: ${key}`);
      
      // 查找中文版本
      const zhDoc = await Document.findOne({ 
        title: mapping.zh, 
        language: 'zh',
        isVisible: true 
      });
      
      // 查找英文版本
      const enDoc = await Document.findOne({ 
        title: mapping.en, 
        language: 'en',
        isVisible: true 
      });

      if (zhDoc && enDoc) {
        console.log(`  ✅ 中文版: ${zhDoc.title} (ID: ${zhDoc._id})`);
        console.log(`  ✅ 英文版: ${enDoc.title} (ID: ${enDoc._id})`);
        console.log(`  🔗 中文URL: /faq/article/${zhDoc._id}`);
        console.log(`  🔗 英文URL: /faq/article/${enDoc._id}`);
        console.log(`  📝 语言切换: 中文 ↔ 英文 应该保持在同一文档`);
      } else {
        console.log(`  ❌ 缺少语言版本:`);
        if (!zhDoc) console.log(`     - 缺少中文版: ${mapping.zh}`);
        if (!enDoc) console.log(`     - 缺少英文版: ${mapping.en}`);
      }
    }

    console.log('\n🎯 预期行为：');
    console.log('=====================================');
    console.log('1. 用户在中文模式下查看"隐私政策"');
    console.log('   URL: /faq/article/687125665e5a2f90ce04825a');
    console.log('');
    console.log('2. 用户切换到英文模式');
    console.log('   系统应该自动跳转到: /faq/article/687125665e5a2f90ce04825c');
    console.log('   显示: "Privacy Policy"');
    console.log('');
    console.log('3. 用户切换回中文模式');
    console.log('   系统应该自动跳转到: /faq/article/687125665e5a2f90ce04825a');
    console.log('   显示: "隐私政策"');

    console.log('\n🔧 实现原理：');
    console.log('=====================================');
    console.log('1. 记录当前文档标题 (previousDocumentTitle)');
    console.log('2. 语言切换时，根据标题映射查找对应语言版本');
    console.log('3. 找到对应文档后，更新URL并显示新文档');
    console.log('4. 如果找不到对应文档，才回退到第一篇文档');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
};

// 主函数
const main = async () => {
  await connectDB();
  await testLanguageSwitching();
  await mongoose.connection.close();
  console.log('\n🔌 数据库连接已关闭');
};

// 执行脚本
main().catch(console.error);
