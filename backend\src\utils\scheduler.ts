import cron from 'node-cron';
import predictionService from '../services/predictionService';
import thirtyMinKlineService from '../services/thirtyMinKlineService';
import solarTermPredictionService from '../services/solarTermPredictionService';
import notificationService from '../services/notificationService';
import logCleanupService from '../services/logCleanupService';
import sseEventManager from '../services/sseEventManager';
import User from '../models/User';
import logger from './logger';

/**
 * 定时任务调度器类
 * 负责管理和调度系统中的定时任务
 */
class Scheduler {
  /**
   * 启动所有定时任务
   * 初始化并启动系统中的所有定时任务
   */
  startAllJobs(): void {
    this.setupPredictionJob();
    this.setupMissingPredictionCheckJob();
    this.setupThirtyMinKlineRefreshJob();
    this.setupThirtyMinKlineHistoryCheckJob();
    this.setupSolarTermPredictionJob();
    this.setupSolarTermPredictionIntegrityCheckJob();
    this.setupUserNotificationCheckJob();
    this.setupUserRoleUpdateJob();
    this.setupSolarTermsUpdateJob();
    this.setupLogCleanupJob();

    // 启动时立即执行一次预测折线完整性检查
    this.runSolarTermPredictionIntegrityCheck();
  }


  /**
   * 设置预测生成任务
   * 每分钟检查当前时间是否为预定义的预测触发时间，如果是则生成新预测
   */
  private setupPredictionJob(): void {
    // 使用cron表达式设置每分钟执行一次
    cron.schedule('* * * * *', async () => {
      const now = new Date();
      const minute = now.getUTCMinutes(); // 使用UTC时间
      const hour = now.getUTCHours();     // 使用UTC时间
      const isOddHour = hour % 2 === 1;

      // 检查当前时间是否为预测触发时间
      // 奇数小时：02分、28分、58分触发
      // 偶数小时：28分触发
      const shouldTrigger = isOddHour
        ? [2, 28, 58].includes(minute)
        : [28].includes(minute);

      if (shouldTrigger) {
        console.log(`[调度器][${now.toISOString()}] 触发预测任务，小时: ${hour}, 分钟: ${minute}`);
        try {
          const prediction = await predictionService.generatePrediction(now);
          if (prediction) {
            console.log(`[调度器][${now.toISOString()}] 预测任务成功完成`);

            // 发布数据库更新通知，让前端重新加载预测数据
            try {
              sseEventManager.publishDatabaseUpdate({
                type: 'prediction-kline',
                message: '预测K线数据已更新',
                predictionId: prediction._id,
                targetTime: prediction.targetStartTime
              }, {
                source: 'scheduler-prediction',
                updateTime: now.toISOString()
              });

              console.log(`[调度器][${now.toISOString()}] 预测K线数据库更新通知已发布`);
            } catch (dbUpdateError) {
              console.error(`[调度器][${now.toISOString()}] 发布预测K线数据库更新通知失败:`, dbUpdateError);
            }
          } else {
            console.log(`[调度器][${now.toISOString()}] 未生成预测`);
          }
        } catch (error) {
          console.error(`[调度器][${now.toISOString()}] 预测任务失败:`, error);
        }
      }
    });
  }

  /**
   * 设置预测数据完整性检查任务
   * 每天凌晨2点自动检查最近3天的预测K线数据，如有缺失则补齐
   */
  private setupMissingPredictionCheckJob(): void {
    // 使用cron表达式设置每天凌晨2点执行一次
    cron.schedule('0 2 * * *', async () => {
      console.log(`[调度器][${new Date().toISOString()}] 开始执行预测数据完整性检查任务`);
      try {
        const filledPredictions = await predictionService.checkAndFillMissingPredictions('BTCUSDT');
        console.log(`[调度器][${new Date().toISOString()}] 预测数据完整性检查任务完成，已补齐${filledPredictions.length}条记录`);
      } catch (error) {
        console.error(`[调度器][${new Date().toISOString()}] 预测数据完整性检查任务失败:`, error);
      }
    });
  }



    /**
     * 每小时第1-2分、31-32分刷新最近5根K线
     */
    setupThirtyMinKlineRefreshJob(): void {
      cron.schedule('1-2,31-32 * * * *', async () => {
        const now = new Date();
        console.log(`[调度器][${now.toISOString()}] 开始执行30分钟K线刷新任务`);
        try {
          const savedCount = await thirtyMinKlineService.fetchLatestCompletedKlines('BTCUSDT');
          if (savedCount > 0) {
            console.log(`[调度器] 刷新任务完成，保存了${savedCount}条K线`);
          } else {
            console.log(`[调度器] 刷新任务无新数据`);
          }
        } catch (error) {
          console.error(`[调度器] 刷新任务失败:`, error);
        }
      });
    }

    /**
     * 每天凌晨1点检查并补齐最近3天的K线
     */
    setupThirtyMinKlineHistoryCheckJob(): void {
      cron.schedule('0 1 * * *', async () => {
        const now = new Date();
        console.log(`[调度器][${now.toISOString()}] 开始执行30分钟K线补齐任务`);
        try {
          const fixedCount = await thirtyMinKlineService.checkAndFillHistoricalData('BTCUSDT');
          if (fixedCount > 0) {
            console.log(`[调度器] 补齐任务完成，修复了${fixedCount}条K线`);
          } else {
            console.log(`[调度器] 数据完整，无需补齐`);
          }
        } catch (error) {
          console.error(`[调度器] 补齐任务失败:`, error);
        }
      });
    }

  /**
   * 设置节气预测折线生成任务
   * 每小时检查当前时间是否为节气预测的触发时间点
   */
  private setupSolarTermPredictionJob(): void {
    // 每小时整点检查一次
    cron.schedule('0 * * * *', async () => {
      const now = new Date();

      console.log(`[调度器][${now.toISOString()}] 检查是否需要生成节气预测折线`);

      try {
        // 检查是否是预测时间点
        const predictionPoint = solarTermPredictionService.isPredictionTimePoint(now);

        if (predictionPoint) {
          console.log(`[调度器][${now.toISOString()}] 开始生成节气预测折线, 周期: ${predictionPoint.cycle}, 预测时长: ${predictionPoint.predictionHours}小时`);

          const prediction = await solarTermPredictionService.generatePrediction(now);

          if (prediction) {
            console.log(`[调度器][${now.toISOString()}] 节气预测折线生成成功, ID: ${prediction._id}, 数据点: ${prediction.predictionData.length}`);

            // 发布数据库更新通知，让前端重新加载预测折线数据
            try {
              sseEventManager.publishDatabaseUpdate({
                type: 'prediction-line',
                message: '预测折线数据已更新',
                predictionId: prediction._id,
                solarTermDate: prediction.solarTermDate,
                dataPoints: prediction.predictionData.length
              }, {
                source: 'scheduler-prediction-line',
                updateTime: now.toISOString(),
                cycle: predictionPoint.cycle
              });

              console.log(`[调度器][${now.toISOString()}] 预测折线数据库更新通知已发布`);
            } catch (dbUpdateError) {
              console.error(`[调度器][${now.toISOString()}] 发布预测折线数据库更新通知失败:`, dbUpdateError);
            }
          } else {
            console.log(`[调度器][${now.toISOString()}] 节气预测折线生成未生成数据`);
          }
        } else {
          console.log(`[调度器][${now.toISOString()}] 当前不是节气预测执行时间点`);
        }
      } catch (error) {
        console.error(`[调度器][${now.toISOString()}] 节气预测折线生成任务失败:`, error);
        }
      });
    }

  /**
   * 设置节气预测折线完整性检查任务
   * 在每天凌晨3点执行，检查并补齐最近5天内的预测
   */
  private setupSolarTermPredictionIntegrityCheckJob(): void {
    // 每天凌晨3点执行
    cron.schedule('0 3 * * *', async () => {
      await this.runSolarTermPredictionIntegrityCheck();
    });

    console.log('[调度器] 节气预测折线完整性检查任务已启动，定时执行');
  }

  /**
   * 执行预测折线完整性检查
   * 检查并补齐最近5天内符合节气预测逻辑的时间点数据
   */
  private async runSolarTermPredictionIntegrityCheck(): Promise<void> {
    const now = new Date();
    console.log(`[调度器][${now.toISOString()}] 开始执行预测折线完整性检查任务...`);

    try {
      const result = await solarTermPredictionService.checkPredictionIntegrity(now);

      if (result.success) {
        // 由于我们不再跳过任何预测点，直接使用返回的消息
        console.log(`[调度器][${now.toISOString()}] 预测折线完整性检查成功: ${result.message}`);
      } else {
        console.error(`[调度器][${now.toISOString()}] 预测折线完整性检查失败: ${result.message}`);
      }
    } catch (error) {
      console.error(`[调度器][${now.toISOString()}] 预测折线完整性检查任务执行异常:`, error);
    }
  }

  /**
   * 设置用户通知检查任务
   * 每天上午10点检查试用期和订阅到期情况，发送提醒通知
   */
  private setupUserNotificationCheckJob(): void {
    // 每天上午10点执行
    cron.schedule('0 10 * * *', async () => {
      const now = new Date();
      console.log(`[调度器][${now.toISOString()}] 开始执行用户通知检查任务`);

      try {
        await this.checkTrialExpiringUsers();
        await this.checkSubscriptionExpiringUsers();
        console.log(`[调度器][${now.toISOString()}] 用户通知检查任务完成`);
      } catch (error) {
        console.error(`[调度器][${now.toISOString()}] 用户通知检查任务失败:`, error);
      }
    });

    console.log('[调度器] 用户通知检查任务已启动，每天上午10点执行');
  }

  /**
   * 检查试用期即将到期的用户
   */
  private async checkTrialExpiringUsers(): Promise<void> {
    try {
      const threeDaysFromNow = new Date();
      threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);

      // 查找试用期在3天内到期的用户
      const expiringUsers = await User.find({
        role: 'trial',
        trialEndsAt: {
          $gte: new Date(),
          $lte: threeDaysFromNow
        }
      });

      for (const user of expiringUsers) {
        const daysRemaining = Math.ceil(
          (new Date(user.trialEndsAt).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
        );

        if (daysRemaining > 0 && daysRemaining <= 3) {
          try {
            await notificationService.createTrialExpiringNotification(user._id, daysRemaining);
            console.log(`[调度器] 为用户 ${user._id} 创建试用期到期提醒，剩余 ${daysRemaining} 天`);
          } catch (error) {
            console.error(`[调度器] 为用户 ${user._id} 创建试用期到期提醒失败:`, error);
          }
        }
      }

      console.log(`[调度器] 试用期到期检查完成，处理了 ${expiringUsers.length} 个用户`);
    } catch (error) {
      console.error('[调度器] 检查试用期到期用户失败:', error);
    }
  }

  /**
   * 检查订阅即将到期的用户
   */
  private async checkSubscriptionExpiringUsers(): Promise<void> {
    try {
      const sevenDaysFromNow = new Date();
      sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7);

      // 查找订阅在7天内到期的用户
      const expiringUsers = await User.find({
        role: 'subscriber',
        'subscription.status': 'active',
        'subscription.endDate': {
          $gte: new Date(),
          $lte: sevenDaysFromNow
        }
      });

      for (const user of expiringUsers) {
        if (user.subscription) {
          const daysRemaining = Math.ceil(
            (new Date(user.subscription.endDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
          );

          if (daysRemaining > 0 && daysRemaining <= 7) {
            try {
              await notificationService.createSubscriptionExpiringNotification(
                user._id,
                user.subscription.plan,
                daysRemaining
              );
              console.log(`[调度器] 为用户 ${user._id} 创建订阅到期提醒，剩余 ${daysRemaining} 天`);
            } catch (error) {
              console.error(`[调度器] 为用户 ${user._id} 创建订阅到期提醒失败:`, error);
            }
          }
        }
      }

      console.log(`[调度器] 订阅到期检查完成，处理了 ${expiringUsers.length} 个用户`);
    } catch (error) {
      console.error('[调度器] 检查订阅到期用户失败:', error);
    }
  }

  /**
   * 设置用户角色状态更新任务
   * 每小时检查并更新过期用户的角色状态
   */
  private setupUserRoleUpdateJob(): void {
    // 每小时执行一次
    cron.schedule('0 * * * *', async () => {
      const now = new Date();
      console.log(`[调度器][${now.toISOString()}] 开始执行用户角色状态更新任务`);

      try {
        await this.updateExpiredTrialUsers();
        await this.updateExpiredSubscriptionUsers();
        console.log(`[调度器][${now.toISOString()}] 用户角色状态更新任务完成`);
      } catch (error) {
        console.error(`[调度器][${now.toISOString()}] 用户角色状态更新任务失败:`, error);
      }
    });

    console.log('[调度器] 用户角色状态更新任务已启动，每小时执行一次');
  }

  /**
   * 设置节气数据更新任务
   * 每年12月1日凌晨4点执行，获取未来2年的节气数据
   */
  private setupSolarTermsUpdateJob(): void {
    // 每年12月1日凌晨4点执行
    cron.schedule('0 4 1 12 *', async () => {
      const now = new Date();
      console.log(`[调度器][${now.toISOString()}] 开始执行节气数据更新任务`);

      try {
        await this.runSolarTermsUpdate();
        console.log(`[调度器][${now.toISOString()}] 节气数据更新任务完成`);
      } catch (error) {
        console.error(`[调度器][${now.toISOString()}] 节气数据更新任务失败:`, error);
      }
    });

    console.log('[调度器] 节气数据更新任务已启动，每年12月1日凌晨4点执行');
  }

  /**
   * 更新过期试用用户的角色状态
   * 将试用期已过期的用户角色从 'trial' 更新为 'normal'
   */
  private async updateExpiredTrialUsers(): Promise<void> {
    try {
      const now = new Date();

      // 批量更新过期的试用用户
      const result = await User.updateMany(
        {
          role: 'trial',
          trialEndsAt: { $lt: now }
        },
        {
          $set: { role: 'normal' }
        }
      );

      if (result.modifiedCount > 0) {
        console.log(`[调度器] 更新了 ${result.modifiedCount} 个过期试用用户的角色状态为普通用户`);
      } else {
        console.log(`[调度器] 没有需要更新的过期试用用户`);
      }
    } catch (error) {
      console.error('[调度器] 更新过期试用用户失败:', error);
    }
  }

  /**
   * 更新过期订阅用户的角色状态
   * 将订阅已过期的用户角色从 'subscriber' 更新为 'normal'，并更新订阅状态
   */
  private async updateExpiredSubscriptionUsers(): Promise<void> {
    try {
      const now = new Date();

      // 批量更新过期的订阅用户
      const result = await User.updateMany(
        {
          role: 'subscriber',
          'subscription.status': 'active',
          'subscription.endDate': { $lt: now }
        },
        {
          $set: {
            role: 'normal',
            'subscription.status': 'expired'
          }
        }
      );

      if (result.modifiedCount > 0) {
        console.log(`[调度器] 更新了 ${result.modifiedCount} 个过期订阅用户的角色状态为普通用户`);
      } else {
        console.log(`[调度器] 没有需要更新的过期订阅用户`);
      }
    } catch (error) {
      console.error('[调度器] 更新过期订阅用户失败:', error);
    }
  }

  /**
   * 执行节气数据更新
   * 调用节气数据更新脚本，获取未来2年的节气数据并更新常量文件
   */
  private async runSolarTermsUpdate(): Promise<void> {
    try {
      console.log('[调度器] 开始执行节气数据更新...');

      // 动态导入节气数据更新模块
      const { getFutureSolarTerms } = require('../../scripts/solarTermCalculator');
      const { updateSolarTermsFile } = require('../../scripts/solarTermFileUpdater');

      // 获取未来2年的节气数据
      const solarTermsData = await getFutureSolarTerms(2);

      if (!solarTermsData || Object.keys(solarTermsData).length === 0) {
        throw new Error('未能获取到有效的节气数据');
      }

      console.log(`[调度器] 成功获取${Object.keys(solarTermsData).length}年的节气数据`);

      // 更新节气数据文件
      const updateSuccess = await updateSolarTermsFile(solarTermsData);

      if (!updateSuccess) {
        throw new Error('节气数据文件更新失败');
      }

      console.log('[调度器] 节气数据更新成功');

    } catch (error) {
      console.error('[调度器] 节气数据更新失败:', error);
      throw error;
    }
  }

  /**
   * 设置日志自动清理任务
   * 每天凌晨3:10执行
   */
  private setupLogCleanupJob(): void {
    cron.schedule('10 3 * * *', async () => {
      const now = new Date();
      logger.info(`[调度器][${now.toISOString()}] 开始执行日志自动清理任务`);

      try {
        await logCleanupService.autoCleanup();
        logger.info('[调度器] 日志自动清理任务完成');
      } catch (error) {
        logger.error('[调度器] 日志清理任务失败', error);
      }
    });
  }
}

export default new Scheduler();