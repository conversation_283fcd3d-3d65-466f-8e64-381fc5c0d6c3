import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';
import systemSettingsService from '../services/systemSettingsService';
import logger from './logger';
import PasswordResetToken from '../models/PasswordResetToken';
import mongoose from 'mongoose';

dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET || 'your-jwt-secret';

// API URLs
const RESEND_API_URL = 'https://api.resend.com/emails';
const BREVO_API_URL = 'https://api.brevo.com/v3/smtp/email';
const MAILERSEND_API_URL = 'https://api.mailersend.com/v1/email';



/**
 * 获取邮件配置
 */
const getEmailConfig = async () => {
  try {
    const settings = await systemSettingsService.getSystemSettings();
    return settings.emailSettings;
  } catch (error) {
    logger.error('获取邮件配置失败', error);
    // 返回默认配置
    return {
      provider: 'resend' as const,
      resend: {
        apiKey: process.env.RESEND_API_KEY || '',
        fromEmail: process.env.EMAIL_FROM || '<EMAIL>',
        enabled: true
      },
      brevo: {
        apiKey: process.env.BREVO_API_KEY || '',
        fromEmail: process.env.EMAIL_FROM || '<EMAIL>',
        enabled: false
      },
      mailersend: {
        apiKey: process.env.MAILERSEND_API_KEY || '',
        fromEmail: process.env.EMAIL_FROM || '<EMAIL>',
        enabled: false
      },
      retryTimes: 3,
      timeout: 30000,
      enableLogs: true
    };
  }
};

/**
 * 使用Resend API发送邮件
 */
const sendEmailWithResend = async (to: string, subject: string, html: string, apiKey: string, fromEmail: string): Promise<boolean> => {
  try {
    const response = await fetch(RESEND_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: fromEmail,
        to: [to],
        subject: subject,
        html: html,
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('Resend API 发送失败:', response.status, errorData);
      return false;
    }

    const result = await response.json();
    console.log('Resend API 发送成功:', result.id);
    return true;
  } catch (error) {
    console.error('Resend API 发送邮件失败:', error);
    return false;
  }
};

/**
 * 使用Brevo API发送邮件
 */
const sendEmailWithBrevo = async (to: string, subject: string, html: string, apiKey: string, fromEmail: string): Promise<boolean> => {
  try {
    const response = await fetch(BREVO_API_URL, {
      method: 'POST',
      headers: {
        'api-key': apiKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sender: { email: fromEmail },
        to: [{ email: to }],
        subject: subject,
        htmlContent: html,
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('Brevo API 发送失败:', response.status, errorData);
      return false;
    }

    const result = await response.json();
    console.log('Brevo API 发送成功:', result.messageId);
    return true;
  } catch (error) {
    console.error('Brevo API 发送邮件失败:', error);
    return false;
  }
};

/**
 * 使用MailerSend API发送邮件
 */
const sendEmailWithMailerSend = async (to: string, subject: string, html: string, apiKey: string, fromEmail: string): Promise<boolean> => {
  try {
    const response = await fetch(MAILERSEND_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: { email: fromEmail },
        to: [{ email: to }],
        subject: subject,
        html: html,
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('MailerSend API 发送失败:', response.status, errorData);
      return false;
    }

    const result = await response.json();
    console.log('MailerSend API 发送成功:', result.id);
    return true;
  } catch (error) {
    console.error('MailerSend API 发送邮件失败:', error);
    return false;
  }
};

/**
 * 统一邮件发送函数 - 支持多个邮件服务商
 */
const sendEmail = async (to: string, subject: string, html: string): Promise<boolean> => {
  try {
    // 获取邮件配置
    const emailConfig = await getEmailConfig();

    // 检查主要服务商配置
    const primaryProvider = emailConfig.provider;
    const primaryConfig = emailConfig[primaryProvider];

    if (!primaryConfig.apiKey || !primaryConfig.enabled) {
      console.error(`邮件发送失败: ${primaryProvider} 未配置或未启用`);

      // 尝试使用备用服务商
      if (emailConfig.fallbackProvider) {
        const fallbackConfig = emailConfig[emailConfig.fallbackProvider];
        if (fallbackConfig.apiKey && fallbackConfig.enabled) {
          console.log(`切换到备用服务商: ${emailConfig.fallbackProvider}`);
          return await sendEmailWithProvider(to, subject, html, emailConfig.fallbackProvider, fallbackConfig.apiKey, fallbackConfig.fromEmail);
        }
      }

      return false;
    }

    // 使用主要服务商发送
    let result = await sendEmailWithProvider(to, subject, html, primaryProvider, primaryConfig.apiKey, primaryConfig.fromEmail);

    // 如果主要服务商失败，尝试备用服务商
    if (!result && emailConfig.fallbackProvider) {
      const fallbackConfig = emailConfig[emailConfig.fallbackProvider];
      if (fallbackConfig.apiKey && fallbackConfig.enabled) {
        console.log(`主服务商失败，切换到备用服务商: ${emailConfig.fallbackProvider}`);
        result = await sendEmailWithProvider(to, subject, html, emailConfig.fallbackProvider, fallbackConfig.apiKey, fallbackConfig.fromEmail);
      }
    }

    return result;
  } catch (error) {
    logger.error('邮件发送失败', error);
    return false;
  }
};

/**
 * 根据服务商发送邮件
 */
const sendEmailWithProvider = async (to: string, subject: string, html: string, provider: string, apiKey: string, fromEmail: string): Promise<boolean> => {
  switch (provider) {
    case 'resend':
      return await sendEmailWithResend(to, subject, html, apiKey, fromEmail);
    case 'brevo':
      return await sendEmailWithBrevo(to, subject, html, apiKey, fromEmail);
    case 'mailersend':
      return await sendEmailWithMailerSend(to, subject, html, apiKey, fromEmail);
    default:
      console.error(`不支持的邮件服务商: ${provider}`);
      return false;
  }
};

// 邮箱验证token功能已移除，现在使用验证码验证方式

/**
 * 生成密码重置 token
 */
export const generatePasswordResetToken = (userId: string): string => {
  return jwt.sign(
    { userId, purpose: 'password-reset' },
    JWT_SECRET,
    { expiresIn: '1h' }
  );
};

// 邮箱验证邮件功能已移除，现在使用验证码验证方式

/**
 * 发送密码重置邮件
 */
export const sendPasswordResetEmail = async (email: string, userId: string): Promise<boolean> => {
  try {
    // 生成重置密码 token
    const resetToken = generatePasswordResetToken(userId);

    // 保存token到数据库（一次性使用）
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1小时后过期
    await PasswordResetToken.createResetToken(
      new mongoose.Types.ObjectId(userId),
      resetToken,
      expiresAt
    );

    // 构建重置密码链接
    const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/reset-password?token=${resetToken}`;

    // 构建邮件内容
    const subject = 'BTC预测 - 重置您的密码';
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">密码重置请求</h2>
        <p>您收到这封邮件是因为您（或其他人）请求重置您的账号密码。</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${resetUrl}"
             style="background-color: #dc3545; color: white; padding: 12px 24px;
                    text-decoration: none; border-radius: 5px; display: inline-block;">
            重置密码
          </a>
        </div>
        <p style="color: #666; font-size: 14px;">
          此链接将在 1 小时后失效。<br>
          如果您没有请求重置密码，请忽略此邮件，您的密码将保持不变。
        </p>
        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
        <p style="color: #999; font-size: 12px;">
          如果按钮无法点击，请复制以下链接到浏览器地址栏：<br>
          <span style="word-break: break-all; word-wrap: break-word; display: block; margin-top: 8px; padding: 8px; background-color: #f5f5f5; border-radius: 4px; font-family: monospace;">${resetUrl}</span>
        </p>
      </div>
    `;

    // 使用统一邮件发送函数
    return await sendEmail(email, subject, html);
  } catch (error) {
    logger.error('发送密码重置邮件失败', error);
    return false;
  }
};

// 邮箱验证token验证功能已移除，现在使用验证码验证方式

/**
 * 验证密码重置 token（仅验证，不标记为已使用）
 */
export const checkPasswordResetToken = async (token: string): Promise<{ userId: string } | null> => {
  try {
    // 首先验证JWT token的有效性
    const decoded = jwt.verify(token, JWT_SECRET) as { userId: string; purpose: string };

    // 验证 token 的用途是否为密码重置
    if (decoded.purpose !== 'password-reset') {
      return null;
    }

    // 仅检查token是否有效，不标记为已使用
    const tokenRecord = await PasswordResetToken.findOne({
      token,
      isUsed: false,
      expiresAt: { $gt: new Date() }
    });

    if (!tokenRecord) {
      return null; // token不存在、已使用或已过期
    }

    return { userId: decoded.userId };
  } catch (error) {
    return null;
  }
};

/**
 * 验证密码重置 token（验证并标记为已使用）
 */
export const verifyPasswordResetToken = async (token: string): Promise<{ userId: string } | null> => {
  try {
    // 首先验证JWT token的有效性
    const decoded = jwt.verify(token, JWT_SECRET) as { userId: string; purpose: string };

    // 验证 token 的用途是否为密码重置
    if (decoded.purpose !== 'password-reset') {
      return null;
    }

    // 验证并使用一次性token（从数据库中检查并标记为已使用）
    const tokenRecord = await PasswordResetToken.validateAndUseToken(token);

    if (!tokenRecord) {
      return null; // token不存在、已使用或已过期
    }

    return { userId: decoded.userId };
  } catch (error) {
    return null;
  }
};

/**
 * 获取邮件模板内容
 */
const getEmailTemplate = (code: string, purpose: 'email-verification' | 'password-reset', language: 'zh' | 'en' = 'zh') => {
  const templates = {
    zh: {
      'email-verification': {
        subject: 'BTC预测 - 邮箱验证码',
        platformName: 'BTC 预测平台',
        title: '邮箱验证',
        greeting: '感谢您注册我们的服务',
        instruction: '请使用以下验证码完成邮箱验证：',
        importantNote: '重要提示：',
        validityNote: '验证码有效期为 <strong>10 分钟</strong>',
        securityNote: '请勿将验证码告诉他人',
        ignoreNote: '如果您没有注册我们的服务，请忽略此邮件',
        footer: '此邮件由系统自动发送，请勿回复<br>© 2024 BTC预测系统. 保留所有权利.'
      },
      'password-reset': {
        subject: 'BTC预测 - 密码重置验证码',
        platformName: 'BTC 预测平台',
        title: '密码重置',
        greeting: '密码重置请求',
        instruction: '您收到这封邮件是因为您请求重置账号密码。请使用以下验证码：',
        importantNote: '安全提示：',
        validityNote: '验证码有效期为 <strong>10 分钟</strong>',
        securityNote: '如果您没有请求重置密码，请立即联系我们',
        ignoreNote: '请勿将验证码告诉他人',
        footer: '此邮件由系统自动发送，请勿回复<br>© 2024 BTC预测系统. 保留所有权利.'
      }
    },
    en: {
      'email-verification': {
        subject: 'BTC Prediction - Email Verification Code',
        platformName: 'BTC Prediction Platform',
        title: 'Email Verification',
        greeting: 'Thank you for registering with our service',
        instruction: 'Please use the following verification code to complete email verification:',
        importantNote: 'Important Notice:',
        validityNote: 'Verification code is valid for <strong>10 minutes</strong>',
        securityNote: 'Do not share this verification code with others',
        ignoreNote: 'If you did not register with our service, please ignore this email',
        footer: 'This email is sent automatically by the system, please do not reply<br>© 2024 BTC Prediction System. All rights reserved.'
      },
      'password-reset': {
        subject: 'BTC Prediction - Password Reset Verification Code',
        platformName: 'BTC Prediction Platform',
        title: 'Password Reset',
        greeting: 'Password Reset Request',
        instruction: 'You received this email because you requested to reset your account password. Please use the following verification code:',
        importantNote: 'Security Notice:',
        validityNote: 'Verification code is valid for <strong>10 minutes</strong>',
        securityNote: 'If you did not request a password reset, please contact us immediately',
        ignoreNote: 'Do not share this verification code with others',
        footer: 'This email is sent automatically by the system, please do not reply<br>© 2024 BTC Prediction System. All rights reserved.'
      }
    }
  };

  const template = templates[language][purpose];
  const borderColor = purpose === 'email-verification' ? '#007bff' : '#dc3545';
  const bgColor = purpose === 'email-verification' ? '#e3f2fd' : '#f8d7da';
  const textColor = purpose === 'email-verification' ? '#1976d2' : '#721c24';

  return {
    subject: template.subject,
    html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #333; margin: 0; font-size: 28px;">${template.platformName}</h1>
            <p style="color: #666; margin: 10px 0 0 0; font-size: 16px;">${template.title}</p>
          </div>

          <div style="background-color: #f8f9fa; border-radius: 8px; padding: 30px; margin: 20px 0;">
            <h2 style="color: #333; margin: 0 0 20px 0; font-size: 24px; text-align: center;">${template.greeting}</h2>
            <p style="color: #666; font-size: 16px; line-height: 1.5; margin: 0 0 25px 0; text-align: center;">
              ${template.instruction}
            </p>

            <div style="text-align: center; margin: 30px 0;">
              <div style="background-color: #ffffff; border: 3px dashed ${borderColor}; border-radius: 12px;
                          padding: 25px; display: inline-block; box-shadow: 0 2px 10px rgba(0,123,255,0.1);">
                <div style="font-size: 36px; font-weight: bold; color: ${borderColor}; letter-spacing: 8px;
                           font-family: 'Courier New', monospace;">${code}</div>
              </div>
            </div>

            <div style="background-color: ${bgColor}; border-left: 4px solid ${borderColor}; padding: 15px; margin: 25px 0; border-radius: 4px;">
              <p style="color: ${textColor}; margin: 0; font-size: 14px;">
                <strong>${template.importantNote}</strong><br>
                • ${template.validityNote}<br>
                • ${template.securityNote}<br>
                • ${template.ignoreNote}
              </p>
            </div>
          </div>

          <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
            <p style="color: #999; font-size: 12px; margin: 0;">
              ${template.footer}
            </p>
          </div>
        </div>
      `
  };
};

/**
 * 发送验证码邮件
 */
export const sendVerificationCodeEmail = async (email: string, code: string, purpose: 'email-verification' | 'password-reset' = 'email-verification', language: 'zh' | 'en' = 'zh'): Promise<boolean> => {
  try {
    const { subject, html } = getEmailTemplate(code, purpose, language);


    // 使用统一邮件发送函数
    const result = await sendEmail(email, subject, html);

    if (result) {
      logger.info(`验证码邮件发送成功: ${email}, 用途: ${purpose}`);
    } else {
      logger.error(`验证码邮件发送失败: ${email}, 用途: ${purpose}`);
    }

    return result;
  } catch (error) {
    logger.error('发送验证码邮件失败', error);
    return false;
  }
};

export default sendVerificationCodeEmail;