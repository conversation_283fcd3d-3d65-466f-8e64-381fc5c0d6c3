/**
 * 图表组合组件
 * 
 * 这个组件将所有拆分的子组件组合在一起，提供与原Chart.tsx相同的API。
 * 包括：图表核心、主K线图系列、预测K线图系列、预测折线图和信息栏。
 */

import React, { useRef, useState, useCallback, useEffect } from 'react';
import { IChartApi, ISeriesApi, MouseEventParams, Time } from 'lightweight-charts';
import { useTranslation } from 'react-i18next';
import ChartCore from './ChartCore';
import MainCandlestickSeries from './MainCandlestickSeries';
import PredictionCandlestickSeries from './PredictionCandlestickSeries';
import PredictionLineChartLayer from '../PredictionLineChartLayer';
import RealTimeLineSeries from '../RealTimeLineSeries';
import ChartInfoBar from '../ChartInfoBar';
import ChartInfoPanel from '../ChartInfoPanel';
import { KLineData, PredictionData } from '../../types/chartTypes';
import chartManager from '../../utils/chartSeriesManager';
import configService from '../../services/configService';

// 添加全屏样式
import '../../styles/fullscreen.css';

// 图表组件属性接口
interface ChartCompositeProps {
  candleData: KLineData[]; // K线数据数组
  predictionData: PredictionData[]; // 预测数据数组
  isLoading?: boolean; // 加载状态
  onLoadMoreHistory?: (oldestTime: number) => void; // 加载更多历史预测数据的回调函数
  onLoadMoreCandleHistory?: (oldestTime: number) => void; // 加载更多历史K线数据的回调函数
  onChartReady?: (chart: IChartApi, mainSeries: ISeriesApi<"Candlestick">) => void; // 图表就绪回调
  showPrediction?: boolean; // 是否显示预测图（默认显示）
  showPredictionLine?: boolean; // 是否显示预测折线（传入false时隐藏）
  chartType?: 'candlestick' | 'line'; // 当前图表类型
  symbol?: string; // 交易对名称
  onChartTypeChange?: (type: 'candlestick' | 'line') => void; // 切换图表类型的回调函数
}

/**
 * 图表组合组件
 * 将所有拆分的子组件组合在一起
 */
const ChartComposite: React.FC<ChartCompositeProps> = ({
  candleData, 
  predictionData, 
  isLoading = false, 
  onLoadMoreHistory, 
  onLoadMoreCandleHistory, 
  onChartReady,
  showPrediction = true,
  showPredictionLine = false,
  chartType = 'candlestick',
  symbol = 'BTC/USDT',
  onChartTypeChange
}) => {
  const { t } = useTranslation();
  const chartRef = useRef<IChartApi | null>(null);
  const mainSeriesRef = useRef<ISeriesApi<"Candlestick"> | null>(null);
  const [isChartReady, setIsChartReady] = useState<boolean>(false);

  // 添加K线数据状态
  const [latestCandle, setLatestCandle] = useState<KLineData | null>(null);
  const [hoveredCandle, setHoveredCandle] = useState<KLineData | null>(null);
  const mainCandleRef = useRef<KLineData[]>([]);

  // 添加水印状态
  const [watermarkText, setWatermarkText] = useState<string>('');

  // 获取水印域名
  useEffect(() => {
    const loadWatermark = async () => {
      try {
        const domain = await configService.getWatermarkDomain();
        console.log('获取到水印域名:', domain);
        setWatermarkText(domain);
      } catch (error) {
        console.error('获取水印域名失败:', error);
        // 使用备用方案
        const hostname = window.location.hostname;
        const fallbackDomain = hostname === 'localhost' || hostname === '127.0.0.1' ? 'BTC预测' : hostname;
        console.log('使用备用水印域名:', fallbackDomain);
        setWatermarkText(fallbackDomain);
      }
    };

    loadWatermark();
  }, []);

  // 监听水印文本变化，更新图表水印
  useEffect(() => {
    console.log('水印文本已更新:', watermarkText);
    if (watermarkText && isChartReady) {
      chartManager.updateWatermark(watermarkText);
    }
  }, [watermarkText, isChartReady]);

  // 保存最新的K线数据
  useEffect(() => {
    if (candleData && candleData.length > 0) {
      mainCandleRef.current = candleData;
      setLatestCandle(candleData[candleData.length - 1]);
    }
  }, [candleData]);

  // 图表就绪处理函数
  const handleChartReady = useCallback((chart: IChartApi) => {
    chartRef.current = chart;
    setIsChartReady(true);
    console.log('图表核心已就绪');
    
    // 添加十字线移动事件监听
    chart.subscribeCrosshairMove((param: MouseEventParams) => {
      if (
        param.point === undefined || 
        param.time === undefined || 
        param.point.x < 0 || 
        param.point.y < 0
      ) {
        // 鼠标移出图表区域，显示最新K线数据
        setHoveredCandle(null);
        return;
      }
      
      // 查找当前时间点对应的K线数据
      const timeValue = param.time as Time;
      const currentData = mainCandleRef.current.find(item => {
        const itemTime = typeof item.time === 'string' ? item.time : item.time.toString();
        const paramTime = typeof timeValue === 'string' ? timeValue : timeValue.toString();
        return itemTime === paramTime;
      });
      
      if (currentData) {
        setHoveredCandle(currentData);
      }
    });
  }, []);

  // 确保图表类型正确应用
  useEffect(() => {
    if (!isChartReady || !chartRef.current) return;
    
    // 使用延时以确保所有系列都已创建
    setTimeout(() => {
      // 确保应用正确的图表类型
      console.log('初始化应用图表类型:', chartType);
      chartManager.switchChartType(chartType);
      
      // 控制预测数据可见性，但根据图表类型有条件地应用
      chartManager.togglePredictionVisibility(showPrediction, false); // K线预测
      
      // 只有在折线图模式下才控制预测折线的可见性
      if (chartType === 'line') {
        chartManager.togglePredictionVisibility(showPredictionLine, true); // 预测折线
      }
    }, 100);
  }, [isChartReady, chartType, showPrediction, showPredictionLine]);

  // 主K线系列就绪处理函数
  const handleMainSeriesReady = useCallback((series: ISeriesApi<"Candlestick">) => {
    mainSeriesRef.current = series;
    
    // 如果提供了onChartReady回调，则调用
    if (onChartReady && chartRef.current) {
      onChartReady(chartRef.current, series);
    }
    
    console.log('主K线系列已就绪');
  }, [onChartReady]);

  // 处理图表类型切换
  const handleChartTypeChange = useCallback((type: 'candlestick' | 'line') => {
    if (!chartRef.current) return;
    
    // 使用图表管理器切换图表类型
    chartManager.switchChartType(type);
    
    // 更新当前图表类型状态
    if (onChartTypeChange) {
      onChartTypeChange(type);
    }
  }, [onChartTypeChange]);

  // 渲染图表组件
  return (
    <div className="chart-composite relative w-full h-full flex flex-col">
      <div className="w-full h-full flex-1 flex flex-col">
        <div className="relative pt-8 sm:pt-8 md:pt-8 h-full flex-1 flex flex-col">
          {/* 信息栏 */}
          <ChartInfoBar 
            symbol={symbol} 
            chartType={chartType}
            onChartTypeChange={handleChartTypeChange}
            className="transition-all"
          />
          
          {/* 价格信息面板 */}
          <ChartInfoPanel 
            latestCandle={latestCandle} 
            hoveredCandle={hoveredCandle} 
            className="absolute top-10 sm:top-10 left-1 z-20 bg-opacity-90 p-2 rounded text-sm text-gray-100"
          />

          {/* 图表核心组件 - 使用图表管理器初始化 */}
          <ChartCore
            onChartReady={handleChartReady}
            chartManager={chartManager}
            containerClassName="w-full h-full flex-1"
            watermarkText={watermarkText}
          >
            {/* 主K线图系列组件 */}
            <MainCandlestickSeries 
              chartApi={chartRef.current}
              isChartReady={isChartReady}
              candleData={candleData}
              isLoading={isLoading}
              onLoadMoreHistory={onLoadMoreCandleHistory}
              onSeriesReady={handleMainSeriesReady}
              candlestickSeries={chartManager.getSeries().candlestick}
            />
            
            {/* 预测K线图系列组件 */}
            <PredictionCandlestickSeries 
              chartApi={chartRef.current}
              isChartReady={isChartReady}
              predictionData={predictionData}
              showPrediction={showPrediction}
              onLoadMoreHistory={onLoadMoreHistory}
              predictionSeries={chartManager.getSeries().predictionCandlestick}
            />
            
            {/* 实时折线图系列组件 */}
            <RealTimeLineSeries
              chartApi={chartRef.current}
              isChartReady={isChartReady}
              candleData={candleData}
              lineSeries={chartManager.getSeries().realtimeLine}
              isVisible={chartType === 'line'}
            />
            
            {/* 预测折线图层 */}
              <PredictionLineChartLayer
                chartApi={chartRef.current}
                isVisible={chartType === 'line' && showPredictionLine}
                lineSeries={chartManager.getSeries().solarPrediction}
              />
          </ChartCore>
          
          {/* 加载中覆盖层 */}
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-cyber-bg/80 backdrop-blur-sm z-10">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-cyber-cyan"></div>
              <span className="ml-3 text-cyber-cyan font-mono">{t('common.loading')}</span>
            </div>
          )}
          
          {/* 无数据覆盖层 */}
          {!isLoading && candleData.length === 0 && (
            <div className="absolute inset-0 flex items-center justify-center bg-background bg-opacity-80 z-10">
              <p className="text-content-secondary text-xl">{t('chart.noData')}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChartComposite; 