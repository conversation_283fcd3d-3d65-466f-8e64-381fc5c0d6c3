/**
 * 图表工具函数
 * 
 * 该文件包含与图表相关的工具函数，用于数据格式化、转换和处理。
 * 这些函数从Chart.tsx组件中提取，以减少组件复杂度，提高代码可重用性。
 */

import { Time, DeepPartial, ChartOptions, LineWidth, LineStyle, PriceFormat, LineSeriesOptions, AreaSeriesOptions, TimeScaleOptions } from 'lightweight-charts';
import { KLineData } from '../types/chartTypes';
import timezoneManager from './timezoneManager';

/**
 * 将字符串或数字类型的值转换为数字
 * @param value 要转换的值
 * @returns 转换后的数字
 */
export const parseNumericValue = (value: string | number): number => {
  return typeof value === 'string' ? parseFloat(value) : value;
};

/**
 * 将Time类型的时间转换为数字
 * @param time lightweight-charts的Time类型
 * @returns 转换后的数字时间戳
 */
export const timeToNumber = (time: Time): number => {
  return typeof time === 'string' ? parseInt(time) : Number(time);
};

/**
 * 格式化价格信息为HTML字符串
 * @param data K线数据
 * @param language 语言代码 ('zh' 或 'en')
 * @returns 格式化后的HTML字符串
 */
export const formatPriceInfo = (data: KLineData, language: string = 'zh'): string => {
  // 使用时区管理器格式化时间，不指定format参数，使用默认的full格式
  const time = timezoneManager.formatTime(Number(data.time));
  const changePercent = ((data.close - data.open) / data.open * 100).toFixed(2);
  const amplitudePercent = ((data.high - data.low) / data.open * 100).toFixed(2);
  const isUp = data.close >= data.open;
  
  // 使用行内样式而非Tailwind类，保证颜色与K线一致
  const upColor = '#26A69A'; // 青色
  const downColor = '#EF5350'; // 红色
  const priceColor = isUp ? upColor : downColor;

  // 根据语言选择标签
  const labels = language === 'en' ? {
    open: 'O',
    high: 'H',
    low: 'L',
    close: 'C',
    change: 'Change',
    amplitude: 'Amplitude'
  } : {
    open: '开',
    high: '高',
    low: '低',
    close: '收',
    change: '涨跌幅',
    amplitude: '振幅'
  };

  return `
    <div style="font-family: 'Fira Code', 'JetBrains Mono', monospace;">
      <span style="color: #8892b0">${time}</span>
      <span style="color: #8892b0">BTC/USDT·30</span>
      <span style="color: #8892b0">${labels.open}：</span><span style="color: ${priceColor}">${data.open.toFixed(2)}</span>
      <span style="color: #8892b0">${labels.high}：</span><span style="color: ${priceColor}">${data.high.toFixed(2)}</span>
      <span style="color: #8892b0">${labels.low}：</span><span style="color: ${priceColor}">${data.low.toFixed(2)}</span>
      <span style="color: #8892b0">${labels.close}：</span><span style="color: ${priceColor}">${data.close.toFixed(2)}</span>
      <span style="color: #8892b0">${labels.change}：</span><span style="color: ${priceColor}">${changePercent}%</span>
      <span style="color: #8892b0">${labels.amplitude}：</span><span style="color: ${priceColor}">${amplitudePercent}%</span>
    </div>
  `;
};

/**
 * 计算K线的趋势方向
 * @param open 开盘价
 * @param close 收盘价
 * @returns 'up' 如果是上涨K线，'down' 如果是下跌K线
 */
export const determineCandleDirection = (open: number, close: number): 'up' | 'down' => {
  return close >= open ? 'up' : 'down';
};

/**
 * 获取基于方向的K线颜色
 * @param direction K线方向 ('up' 或 'down')
 * @param opacity 透明度 (0-1)
 * @returns 颜色字符串
 */
export const getCandleColor = (direction: 'up' | 'down', opacity: number = 0.6): string => {
  return direction === 'up' 
    ? `rgba(38, 166, 154, ${opacity})` // 青色
    : `rgba(239, 83, 80, ${opacity})`; // 红色
};

/**
 * 获取基于方向的K线边框颜色
 * @param direction K线方向 ('up' 或 'down')
 * @param opacity 透明度 (0-1)
 * @returns 颜色字符串
 */
export const getCandleBorderColor = (direction: 'up' | 'down', opacity: number = 0.8): string => {
  return direction === 'up' 
    ? `rgba(38, 166, 154, ${opacity})` // 青色
    : `rgba(239, 83, 80, ${opacity})`; // 红色
};

/**
 * 获取水印配置选项
 * @param domainText 域名文本
 * @returns 水印配置对象
 */
export const getWatermarkOptions = (domainText?: string) => {
  console.log('getWatermarkOptions 调用，域名文本:', domainText);

  if (!domainText) {
    console.log('域名文本为空，水印不可见');
    return { visible: false };
  }

  const watermarkConfig = {
    visible: true,
    text: domainText,
    fontSize: 88,                              // 调整字体大小
    color: 'rgba(255, 255, 255, 0.15)',       // 调整颜色和透明度
    horzAlign: 'center' as const,              // 水平对齐
    vertAlign: 'center' as const,              // 垂直对齐
  };

  console.log('水印配置:', watermarkConfig);
  return watermarkConfig;
};

/**
 * 创建图表默认配置
 * @param containerWidth 容器宽度
 * @param watermarkText 水印文本（可选）
 * @returns 图表配置对象
 */
export const getDefaultChartOptions = (containerWidth: number, watermarkText?: string): DeepPartial<ChartOptions> => {
  return {
    width: containerWidth,
    height:740,
    layout: {
      background: { color: 'rgba(18, 20, 24, 0.85)' }, // 专业图表背景：中性深灰，高对比度
      textColor: '#e2e8f0', // cyber-text
    },
    grid: {
      vertLines: { color: 'rgba(0, 245, 255, 0.1)' }, // cyber-cyan/10
      horzLines: { color: 'rgba(0, 245, 255, 0.1)' }, // cyber-cyan/10
    },
    // 添加水印配置
    watermark: getWatermarkOptions(watermarkText),
    timeScale: {
      timeVisible: true,
      secondsVisible: false,
      borderColor: '#2d2e30',
      barSpacing: 6,
      rightOffset: 50,
      minimumHeight: 40, // 增加时间轴的最小高度
      tickMarkMaxCharacterLength: 12, // 增加标签最大长度以避免重叠
      // 使用时区敏感的时间格式化函数
      tickMarkFormatter: (time: number): string => {
        const date = timezoneManager.convertToCurrentTimezone(time);
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        return `${hours}:${minutes}`;
      },
    },
    // 添加右侧价格轴配置
    rightPriceScale: {
      borderVisible: true,  // 显示边框
      borderColor: '#2d2e30', // 深色边框，与主题匹配
      scaleMargins: {
        top: 0.1,
        bottom: 0.2,
      },
      visible: true,
    },
    crosshair: {
      mode: 0,
      vertLine: {
        color: 'rgba(255, 255, 255, 0.4)', 
        width: 1 as LineWidth, 
        style: 0 as LineStyle,
        visible: true,
        labelVisible: true,
      },
      horzLine: {
        color: 'rgba(255, 255, 255, 0.4)', 
        width: 1 as LineWidth,
        style: 0 as LineStyle,
        visible: true,
        labelVisible: true,
      }
    },
    // 设置本地化配置
    localization: {
      // 修改鼠标悬停时间格式为 yyyy-MM-dd HH:mm
      timeFormatter: (timestamp: number): string => {
        const date = timezoneManager.convertToCurrentTimezone(timestamp);
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      },
      // 使用日期格式，而不是自定义formatter
      dateFormat: 'yyyy-MM-dd',
    },
  };
};

/**
 * 获取主K线图系列配置
 * @returns 主K线图系列配置对象
 */
export const getMainSeriesOptions = () => {
  return {
    upColor: '#26A69A',    // 青色上涨K线
    downColor: '#EF5350',  // 红色下跌K线
    borderVisible: false,
    wickUpColor: '#26A69A', // 青色上涨影线
    wickDownColor: '#EF5350', // 红色下跌影线
  };
};

/**
 * 获取预测K线图系列配置
 * @returns 预测K线图系列配置对象
 */
export const getPredictionSeriesOptions = () => {
  return {
    upColor: 'rgba(38, 166, 154, 0.8)',    // 半透明青色
    downColor: 'rgba(239, 83, 80, 0.8)',   // 半透明红色
    borderVisible: true,
    wickVisible: true,
    borderUpColor: '#26A69A',   // 青色边框
    borderDownColor: '#EF5350', // 红色边框
    wickUpColor: '#26A69A',     // 青色影线
    wickDownColor: '#EF5350',   // 红色影线
    priceFormat: {
      type: 'price' as const,
      precision: 2,
      minMove: 0.01,
    },
    priceScaleId: 'prediction',
    lastValueVisible: false,
    priceLineVisible: false,
  };
};

/**
 * 获取预测K线价格坐标轴配置
 * @returns 预测坐标轴配置对象
 */
export const getPredictionPriceScaleOptions = () => {
  return {
    scaleMargins: {
      top: 0.7,  // 将预测K线显示在图表下部30%的区域
      bottom: 0.0,
    },
    visible: true,
    borderVisible: true,
    borderColor: '#FFD700',
    autoScale: true,
    alignLabels: true,
    entireTextOnly: false,
    mode: 0,
    invertScale: false,
  };
};

/**
 * 获取实时折线系列选项
 * @returns 实时折线系列选项
 */
export const getRealtimeLineOptions = (): DeepPartial<LineSeriesOptions> => {
  return {
    color: '#00FFAA', // 高对比度青绿色组合 - 实时数据
    lineWidth: 2 as LineWidth,   // 标准线宽，不加粗
    lineStyle: 0 as LineStyle,   // 实线
    crosshairMarkerVisible: true,
    crosshairMarkerRadius: 4,
    crosshairMarkerBorderColor: '#00FFAA',
    crosshairMarkerBackgroundColor: '#ffffff',
    lastValueVisible: true,
    priceLineVisible: true,
    priceLineWidth: 1,
    priceLineColor: '#00FFAA',
    visible: false, // 默认不可见，由图表类型控制
  };
};

/**
 * 获取节气预测折线系列选项
 * @returns 节气预测面积图系列选项
 */
export const getSolarPredictionLineOptions = (): DeepPartial<AreaSeriesOptions> => {
  return {
    lineColor: '#8B5FBF', // 神秘蓝紫色 - 预测的神秘感
    lineWidth: 2 as LineWidth,
    lineStyle: 0 as LineStyle, // 实线，不用虚线
    lastValueVisible: false, // 不显示最后的值标签
    priceLineVisible: false, // 不显示价格线
    crosshairMarkerVisible: true,
    crosshairMarkerRadius: 4,
    crosshairMarkerBorderColor: '#8B5FBF',
    crosshairMarkerBackgroundColor: 'rgba(139, 95, 191, 0.3)',
    // 面积图填充颜色 - 蓝紫色渐变，神秘感
    topColor: 'rgba(139, 95, 191, 0.9)',
    bottomColor: 'rgba(139, 95, 191, 0.1)',
    // 使用单独的价格坐标轴
    priceScaleId: 'prediction-line',
    visible: false, // 默认不可见，由图表类型控制
  };
};