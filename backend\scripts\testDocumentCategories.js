const mongoose = require('mongoose');

// 直接定义Document模型
const documentSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  content: {
    type: String,
    required: true
  },
  category: {
    type: String,
    enum: ['guide', 'faq', 'announcement'],
    required: true,
    index: true
  },
  language: {
    type: String,
    enum: ['zh', 'en'],
    required: true,
    index: true
  },
  isVisible: {
    type: Boolean,
    default: true,
    index: true
  },
  order: {
    type: Number,
    default: 0,
    index: true
  },
  originalId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Document',
    default: null
  }
}, {
  timestamps: true
});

const Document = mongoose.model('Document', documentSchema);

// 连接数据库
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/btc-prediction');
    console.log('MongoDB 连接成功');
  } catch (error) {
    console.error('MongoDB 连接失败:', error);
    process.exit(1);
  }
};

// 测试文档分类
const testDocumentCategories = async () => {
  try {
    console.log('测试文档分类功能...\n');

    // 测试各个分类的文档
    const categories = ['guide', 'faq', 'announcement'];
    const languages = ['zh', 'en'];

    for (const category of categories) {
      console.log(`📂 分类: ${category}`);
      console.log('=====================================');
      
      for (const language of languages) {
        const docs = await Document.find({
          category: category,
          language: language,
          isVisible: true
        }).sort({ order: 1 });

        console.log(`  🌐 语言: ${language} (${docs.length} 个文档)`);
        
        if (docs.length > 0) {
          docs.forEach((doc, index) => {
            console.log(`    ${index + 1}. ${doc.title} (order: ${doc.order})`);
            console.log(`       ID: ${doc._id}`);
            console.log(`       URL: /${category}/article/${doc._id}`);
          });
        } else {
          console.log(`    暂无文档`);
        }
        console.log('');
      }
    }

    console.log('🎯 路由测试：');
    console.log('=====================================');
    console.log('访问 /docs -> 显示 guide 分类文档');
    console.log('访问 /faq -> 显示 faq 分类文档');
    console.log('访问 /announcement -> 显示 announcement 分类文档');
    console.log('');
    console.log('🔗 重定向测试：');
    console.log('访问 /terms -> 重定向到服务条款文档');
    console.log('访问 /privacy -> 重定向到隐私政策文档');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
};

// 主函数
const main = async () => {
  await connectDB();
  await testDocumentCategories();
  await mongoose.connection.close();
  console.log('\n🔌 数据库连接已关闭');
};

// 执行脚本
main().catch(console.error);
