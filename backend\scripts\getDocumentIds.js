const mongoose = require('mongoose');

// 直接定义Document模型
const documentSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  content: {
    type: String,
    required: true
  },
  category: {
    type: String,
    enum: ['guide', 'faq', 'announcement'],
    required: true,
    index: true
  },
  language: {
    type: String,
    enum: ['zh', 'en'],
    required: true,
    index: true
  },
  isVisible: {
    type: Boolean,
    default: true,
    index: true
  },
  order: {
    type: Number,
    default: 0,
    index: true
  },
  originalId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Document',
    default: null
  }
}, {
  timestamps: true
});

const Document = mongoose.model('Document', documentSchema);

// 连接数据库
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/btc-prediction');
    console.log('MongoDB 连接成功');
  } catch (error) {
    console.error('MongoDB 连接失败:', error);
    process.exit(1);
  }
};

// 获取文档ID
const getDocumentIds = async () => {
  try {
    console.log('查询服务条款和隐私政策文档ID...\n');

    // 查找服务条款文档
    const termsZh = await Document.findOne({ title: '服务条款', language: 'zh' });
    const termsEn = await Document.findOne({ title: 'Terms of Service', language: 'en' });
    
    // 查找隐私政策文档
    const privacyZh = await Document.findOne({ title: '隐私政策', language: 'zh' });
    const privacyEn = await Document.findOne({ title: 'Privacy Policy', language: 'en' });

    console.log('📋 文档ID信息：');
    console.log('=====================================');
    
    if (termsZh) {
      console.log(`🔗 服务条款（中文）: ${termsZh._id}`);
      console.log(`   URL: http://localhost:3000/faq/article/${termsZh._id}`);
    } else {
      console.log('❌ 服务条款（中文）未找到');
    }

    if (termsEn) {
      console.log(`🔗 Terms of Service（英文）: ${termsEn._id}`);
      console.log(`   URL: http://localhost:3000/faq/article/${termsEn._id}`);
    } else {
      console.log('❌ Terms of Service（英文）未找到');
    }

    if (privacyZh) {
      console.log(`🔗 隐私政策（中文）: ${privacyZh._id}`);
      console.log(`   URL: http://localhost:3000/faq/article/${privacyZh._id}`);
    } else {
      console.log('❌ 隐私政策（中文）未找到');
    }

    if (privacyEn) {
      console.log(`🔗 Privacy Policy（英文）: ${privacyEn._id}`);
      console.log(`   URL: http://localhost:3000/faq/article/${privacyEn._id}`);
    } else {
      console.log('❌ Privacy Policy（英文）未找到');
    }

    console.log('\n=====================================');
    console.log('💡 接下来需要修改前端路由：');
    console.log('1. /terms -> 重定向到对应的服务条款文档');
    console.log('2. /privacy -> 重定向到对应的隐私政策文档');
    console.log('3. 根据用户语言选择中文或英文版本');

  } catch (error) {
    console.error('❌ 查询文档失败:', error);
  }
};

// 主函数
const main = async () => {
  await connectDB();
  await getDocumentIds();
  await mongoose.connection.close();
  console.log('\n🔌 数据库连接已关闭');
};

// 执行脚本
main().catch(console.error);
