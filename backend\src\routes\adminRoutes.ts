import express from 'express';
import adminController from '../controllers/adminController';

import systemSettingsController from '../controllers/systemSettingsController';
import paymentAdminController from '../controllers/paymentAdminController';
import logController from '../controllers/logController';
import adminFeedbackRoutes from './adminFeedbackRoutes';
import adminDocumentRoutes from './adminDocumentRoutes';
import toolsRouter from './admin/tools';
import authenticate, { isAdmin } from '../middlewares/authMiddleware';

const router = express.Router();

// 管理员登录
router.post('/login', adminController.adminLogin);

// 所有管理员API需要先经过认证和权限验证
router.use(authenticate);
router.use(isAdmin);

// 用户管理
router.get('/users', adminController.getUsers);
router.post('/users', adminController.createUser);
router.patch('/users/:id', adminController.updateUserRole);
router.delete('/users/:id', adminController.deleteUser);
router.post('/users/:id/ban', adminController.banUser);
router.post('/users/:id/unban', adminController.unbanUser);
router.patch('/users/:id/subscription', adminController.updateUserSubscription);

// 系统设置管理
router.get('/settings', systemSettingsController.getSystemSettings);
router.put('/settings', systemSettingsController.updateSystemSettings);
router.post('/settings/test-email-connection', systemSettingsController.testEmailConnection);

// 系统日志管理
router.get('/logs/files', logController.getLogFiles);
router.get('/logs/download/:filename', logController.downloadLogFile);
router.post('/logs/cleanup', logController.manualCleanup);
router.get('/logs/settings', logController.getLogSettings);
router.put('/logs/settings', logController.updateLogSettings);

// 支付管理
router.get('/payments', paymentAdminController.getPayments);
router.get('/payments/stats', paymentAdminController.getPaymentStats);
router.get('/payments/:id', paymentAdminController.getPaymentDetails);
router.patch('/payments/:id', paymentAdminController.updatePaymentStatus);



// 用户反馈管理
router.use('/feedback', adminFeedbackRoutes);

// 文档管理
router.use('/docs', adminDocumentRoutes);

// 文档图片上传
router.post('/docs/upload-image', systemSettingsController.uploadDocumentImage);

// 系统工具
router.use('/tools', toolsRouter);

export default router;