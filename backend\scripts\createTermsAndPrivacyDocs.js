const mongoose = require('mongoose');

// 直接定义Document模型，避免TypeScript导入问题
const documentSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  content: {
    type: String,
    required: true
  },
  category: {
    type: String,
    enum: ['guide', 'faq', 'announcement'],
    required: true,
    index: true
  },
  language: {
    type: String,
    enum: ['zh', 'en'],
    required: true,
    index: true
  },
  isVisible: {
    type: Boolean,
    default: true,
    index: true
  },
  order: {
    type: Number,
    default: 0,
    index: true
  },
  originalId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Document',
    default: null
  }
}, {
  timestamps: true
});

// 创建复合索引
documentSchema.index({ category: 1, language: 1, isVisible: 1, order: 1 });
documentSchema.index({ language: 1, isVisible: 1, order: 1 });

const Document = mongoose.model('Document', documentSchema);

// 连接数据库
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/btc-prediction');
    console.log('MongoDB 连接成功');
  } catch (error) {
    console.error('MongoDB 连接失败:', error);
    process.exit(1);
  }
};

// 服务条款内容（中文）
const termsContentZh = `# 服务条款

**最后更新时间：2024年12月**

欢迎使用我们的BTC预测服务。在使用本服务之前，请仔细阅读以下服务条款。

## 1. 服务说明

### 1.1 服务内容
- 我们提供基于技术分析的BTC价格预测服务
- 包括实时K线图表、预测数据分析等功能
- 服务仅供参考，不构成投资建议

### 1.2 服务性质
- 本服务为数据分析工具，不提供投资咨询
- 所有预测数据仅基于历史数据和技术指标
- 用户应独立判断并承担投资风险

## 2. 用户责任

### 2.1 账户安全
- 用户有责任保护账户安全
- 不得与他人共享账户信息
- 发现异常活动应立即通知我们

### 2.2 合规使用
- 不得将服务用于非法目的
- 不得干扰或破坏服务正常运行
- 遵守当地法律法规

## 3. 免责声明

### 3.1 投资风险
- 加密货币投资存在高风险
- 价格可能大幅波动，可能导致损失
- 用户应根据自身情况谨慎投资

### 3.2 服务限制
- 我们不保证服务的连续性和准确性
- 技术故障或维护可能导致服务中断
- 预测结果不保证准确性

## 4. 订阅服务

### 4.1 付费服务
- 部分功能需要付费订阅
- 订阅费用按照当前价格表执行
- 支持多种订阅周期选择

### 4.2 退款政策
- 订阅服务一经激活，原则上不予退款
- 特殊情况下的退款申请将个案处理
- 技术故障导致的服务中断将相应延长服务期

## 5. 隐私保护

### 5.1 数据收集
- 我们仅收集必要的用户信息
- 严格保护用户隐私和数据安全
- 详细信息请参阅隐私政策

### 5.2 数据使用
- 用户数据仅用于提供服务
- 不会向第三方出售用户信息
- 遵守相关数据保护法规

## 6. 服务变更

### 6.1 条款修改
- 我们保留修改服务条款的权利
- 重大变更将提前通知用户
- 继续使用服务视为接受新条款

### 6.2 服务调整
- 我们可能调整或终止部分服务功能
- 将尽力提前通知用户相关变更
- 保障用户的合理权益

## 7. 争议解决

### 7.1 适用法律
- 本条款受相关法律管辖
- 争议优先通过协商解决
- 必要时通过法律途径解决

### 7.2 联系方式
- 如有疑问或争议，请及时联系我们
- 我们将在合理时间内回复和处理
- 保持开放和诚信的沟通态度

## 8. 其他条款

### 8.1 条款效力
- 本条款构成完整的服务协议
- 如部分条款无效，不影响其他条款效力
- 未行使权利不构成权利放弃

### 8.2 生效时间
- 本条款自发布之日起生效
- 适用于所有使用本服务的用户
- 定期审查和更新条款内容

---

感谢您选择我们的服务，如有任何问题，请随时联系我们。`;

// 服务条款内容（英文）
const termsContentEn = `# Terms of Service

**Last Updated: December 2024**

Welcome to our BTC prediction service. Please read these Terms of Service carefully before using our service.

## 1. Service Description

### 1.1 Service Content
- We provide BTC price prediction services based on technical analysis
- Including real-time candlestick charts, prediction data analysis, and other features
- Services are for reference only and do not constitute investment advice

### 1.2 Service Nature
- This service is a data analysis tool and does not provide investment consulting
- All prediction data is based solely on historical data and technical indicators
- Users should make independent judgments and bear investment risks

## 2. User Responsibilities

### 2.1 Account Security
- Users are responsible for protecting account security
- Do not share account information with others
- Report any suspicious activity immediately

### 2.2 Compliant Use
- Do not use the service for illegal purposes
- Do not interfere with or disrupt normal service operation
- Comply with local laws and regulations

## 3. Disclaimer

### 3.1 Investment Risks
- Cryptocurrency investment involves high risks
- Prices may fluctuate significantly and may result in losses
- Users should invest cautiously based on their own circumstances

### 3.2 Service Limitations
- We do not guarantee service continuity and accuracy
- Technical failures or maintenance may cause service interruptions
- Prediction results are not guaranteed to be accurate

## 4. Subscription Services

### 4.1 Paid Services
- Some features require paid subscription
- Subscription fees are based on current pricing
- Multiple subscription periods available

### 4.2 Refund Policy
- Subscription services are generally non-refundable once activated
- Refund requests in special circumstances will be handled case by case
- Service interruptions due to technical failures will extend service period accordingly

## 5. Privacy Protection

### 5.1 Data Collection
- We only collect necessary user information
- Strictly protect user privacy and data security
- Please refer to our Privacy Policy for details

### 5.2 Data Usage
- User data is used solely for service provision
- We do not sell user information to third parties
- Comply with relevant data protection regulations

## 6. Service Changes

### 6.1 Terms Modification
- We reserve the right to modify these Terms of Service
- Users will be notified in advance of significant changes
- Continued use of the service constitutes acceptance of new terms

### 6.2 Service Adjustments
- We may adjust or terminate certain service features
- We will make reasonable efforts to notify users of changes in advance
- Protect users' legitimate rights and interests

## 7. Dispute Resolution

### 7.1 Applicable Law
- These terms are governed by applicable law
- Disputes should be resolved through negotiation first
- Legal remedies may be pursued when necessary

### 7.2 Contact Information
- Please contact us promptly with any questions or disputes
- We will respond and handle issues within a reasonable time
- Maintain open and honest communication

## 8. Other Terms

### 8.1 Terms Validity
- These terms constitute the complete service agreement
- If any provision is invalid, it does not affect other provisions
- Failure to exercise rights does not constitute waiver of rights

### 8.2 Effective Date
- These terms are effective from the date of publication
- Apply to all users of this service
- Terms are regularly reviewed and updated

---

Thank you for choosing our service. Please contact us if you have any questions.`;

// 隐私政策内容（中文）
const privacyContentZh = `# 隐私政策

**最后更新时间：2024年12月**

我们重视您的隐私权，本隐私政策说明我们如何收集、使用和保护您的个人信息。

## 1. 信息收集

### 1.1 账户信息
- 邮箱地址（用于账户注册和登录）
- 密码（加密存储）
- 用户角色和订阅状态

### 1.2 使用数据
- 登录时间和频率
- 功能使用情况
- 设备和浏览器信息
- IP地址（用于安全防护）

### 1.3 可选信息
- 用户反馈和建议
- 客服沟通记录
- 邀请码使用情况

## 2. 信息使用

### 2.1 服务提供
- 验证用户身份和权限
- 提供个性化服务体验
- 处理订阅和支付
- 发送重要通知

### 2.2 服务改进
- 分析用户行为模式
- 优化产品功能
- 提升服务质量
- 开发新功能

### 2.3 安全保护
- 防范欺诈和滥用
- 检测异常活动
- 保护系统安全
- 遵守法律要求

## 3. 信息保护

### 3.1 技术措施
- 数据传输加密（HTTPS）
- 密码哈希存储
- 访问权限控制
- 定期安全审计

### 3.2 管理措施
- 员工隐私培训
- 最小权限原则
- 数据访问日志
- 安全事件响应

### 3.3 存储安全
- 数据备份和恢复
- 服务器安全防护
- 数据中心物理安全
- 定期漏洞扫描

## 4. 信息共享

### 4.1 不会共享
- 我们不会出售您的个人信息
- 不会向第三方营销公司提供信息
- 不会用于与服务无关的目的

### 4.2 有限共享
- 法律要求的情况下
- 保护用户和公众安全
- 防范欺诈和违法行为
- 经用户明确同意

### 4.3 服务提供商
- 支付处理服务（NOWPayments）
- 云服务提供商
- 安全服务提供商
- 均签署保密协议

## 5. 用户权利

### 5.1 访问权利
- 查看个人信息
- 了解数据使用情况
- 获取数据副本
- 验证数据准确性

### 5.2 控制权利
- 更新个人信息
- 删除账户数据
- 撤回同意
- 限制数据处理

### 5.3 申诉权利
- 对数据处理提出异议
- 向监管机构投诉
- 寻求法律救济
- 获得及时回应

## 6. 数据保留

### 6.1 保留期限
- 活跃账户：持续保留
- 注销账户：30天后删除
- 法律要求：按法规保留
- 安全日志：最长2年

### 6.2 删除政策
- 用户主动删除
- 账户长期不活跃
- 服务终止
- 法律义务结束

## 7. Cookie使用

### 7.1 Cookie类型
- 必要Cookie（身份验证）
- 功能Cookie（用户偏好）
- 分析Cookie（使用统计）
- 安全Cookie（防护措施）

### 7.2 Cookie管理
- 浏览器设置控制
- 选择性接受
- 定期清理
- 透明使用

## 8. 未成年人保护

### 8.1 年龄限制
- 服务面向18岁以上用户
- 不主动收集未成年人信息
- 发现后立即删除
- 通知监护人

### 8.2 特殊保护
- 加强隐私保护
- 限制数据收集
- 优先删除权利
- 监护人参与

## 9. 国际传输

### 9.1 数据传输
- 主要在本地处理
- 必要时跨境传输
- 确保同等保护水平
- 遵守传输规则

### 9.2 保护措施
- 标准合同条款
- 认证机制
- 加密传输
- 定期评估

## 10. 政策更新

### 10.1 更新通知
- 重大变更提前通知
- 网站公告
- 邮件通知
- 继续使用视为同意

### 10.2 版本管理
- 保留历史版本
- 标注更新时间
- 说明主要变更
- 提供对比查看

## 11. 联系我们

如果您对本隐私政策有任何疑问或需要行使您的权利，请通过以下方式联系我们：

- 通过网站反馈功能
- 发送邮件至客服邮箱
- 我们将在合理时间内回复

---

我们承诺保护您的隐私权，感谢您的信任。`;

// 隐私政策内容（英文）
const privacyContentEn = `# Privacy Policy

**Last Updated: December 2024**

We value your privacy rights. This Privacy Policy explains how we collect, use, and protect your personal information.

## 1. Information Collection

### 1.1 Account Information
- Email address (for account registration and login)
- Password (encrypted storage)
- User role and subscription status

### 1.2 Usage Data
- Login time and frequency
- Feature usage patterns
- Device and browser information
- IP address (for security protection)

### 1.3 Optional Information
- User feedback and suggestions
- Customer service communications
- Invitation code usage

## 2. Information Use

### 2.1 Service Provision
- Verify user identity and permissions
- Provide personalized service experience
- Process subscriptions and payments
- Send important notifications

### 2.2 Service Improvement
- Analyze user behavior patterns
- Optimize product features
- Enhance service quality
- Develop new features

### 2.3 Security Protection
- Prevent fraud and abuse
- Detect abnormal activities
- Protect system security
- Comply with legal requirements

## 3. Information Protection

### 3.1 Technical Measures
- Data transmission encryption (HTTPS)
- Password hash storage
- Access control
- Regular security audits

### 3.2 Administrative Measures
- Employee privacy training
- Principle of least privilege
- Data access logging
- Security incident response

### 3.3 Storage Security
- Data backup and recovery
- Server security protection
- Data center physical security
- Regular vulnerability scanning

## 4. Information Sharing

### 4.1 We Will Not Share
- We do not sell your personal information
- We do not provide information to third-party marketing companies
- We do not use information for purposes unrelated to our service

### 4.2 Limited Sharing
- When required by law
- To protect user and public safety
- To prevent fraud and illegal activities
- With explicit user consent

### 4.3 Service Providers
- Payment processing services (NOWPayments)
- Cloud service providers
- Security service providers
- All bound by confidentiality agreements

## 5. User Rights

### 5.1 Access Rights
- View personal information
- Understand data usage
- Obtain data copies
- Verify data accuracy

### 5.2 Control Rights
- Update personal information
- Delete account data
- Withdraw consent
- Restrict data processing

### 5.3 Appeal Rights
- Object to data processing
- Complain to regulatory authorities
- Seek legal remedies
- Receive timely responses

## 6. Data Retention

### 6.1 Retention Period
- Active accounts: Continuously retained
- Deleted accounts: Deleted after 30 days
- Legal requirements: Retained as per regulations
- Security logs: Maximum 2 years

### 6.2 Deletion Policy
- User-initiated deletion
- Long-term inactive accounts
- Service termination
- End of legal obligations

## 7. Cookie Usage

### 7.1 Cookie Types
- Essential cookies (authentication)
- Functional cookies (user preferences)
- Analytics cookies (usage statistics)
- Security cookies (protection measures)

### 7.2 Cookie Management
- Browser settings control
- Selective acceptance
- Regular cleanup
- Transparent usage

## 8. Minor Protection

### 8.1 Age Restrictions
- Service is for users 18 and above
- We do not actively collect minor information
- Immediate deletion upon discovery
- Guardian notification

### 8.2 Special Protection
- Enhanced privacy protection
- Limited data collection
- Priority deletion rights
- Guardian involvement

## 9. International Transfer

### 9.1 Data Transfer
- Primarily processed locally
- Cross-border transfer when necessary
- Ensure equivalent protection level
- Comply with transfer rules

### 9.2 Protection Measures
- Standard contractual clauses
- Certification mechanisms
- Encrypted transmission
- Regular assessments

## 10. Policy Updates

### 10.1 Update Notifications
- Advance notice for major changes
- Website announcements
- Email notifications
- Continued use implies consent

### 10.2 Version Management
- Retain historical versions
- Mark update times
- Explain major changes
- Provide comparison view

## 11. Contact Us

If you have any questions about this Privacy Policy or need to exercise your rights, please contact us through:

- Website feedback function
- Email to customer service
- We will respond within a reasonable time

---

We are committed to protecting your privacy rights. Thank you for your trust.`;

// 创建文档的函数
const createDocuments = async () => {
  try {
    console.log('开始创建服务条款和隐私政策文档...');

    // 检查是否已存在相同标题的文档
    const existingTermsZh = await Document.findOne({ title: '服务条款', language: 'zh' });
    const existingTermsEn = await Document.findOne({ title: 'Terms of Service', language: 'en' });
    const existingPrivacyZh = await Document.findOne({ title: '隐私政策', language: 'zh' });
    const existingPrivacyEn = await Document.findOne({ title: 'Privacy Policy', language: 'en' });

    // 创建服务条款文档（中文）
    if (!existingTermsZh) {
      const termsZh = new Document({
        title: '服务条款',
        content: termsContentZh,
        category: 'faq',
        language: 'zh',
        isVisible: true,
        order: 1
      });
      await termsZh.save();
      console.log('✅ 服务条款（中文）创建成功');
    } else {
      console.log('⚠️  服务条款（中文）已存在，跳过创建');
    }

    // 创建服务条款文档（英文）
    if (!existingTermsEn) {
      const termsEn = new Document({
        title: 'Terms of Service',
        content: termsContentEn,
        category: 'faq',
        language: 'en',
        isVisible: true,
        order: 1
      });
      await termsEn.save();
      console.log('✅ 服务条款（英文）创建成功');
    } else {
      console.log('⚠️  服务条款（英文）已存在，跳过创建');
    }

    // 创建隐私政策文档（中文）
    if (!existingPrivacyZh) {
      const privacyZh = new Document({
        title: '隐私政策',
        content: privacyContentZh,
        category: 'faq',
        language: 'zh',
        isVisible: true,
        order: 2
      });
      await privacyZh.save();
      console.log('✅ 隐私政策（中文）创建成功');
    } else {
      console.log('⚠️  隐私政策（中文）已存在，跳过创建');
    }

    // 创建隐私政策文档（英文）
    if (!existingPrivacyEn) {
      const privacyEn = new Document({
        title: 'Privacy Policy',
        content: privacyContentEn,
        category: 'faq',
        language: 'en',
        isVisible: true,
        order: 2
      });
      await privacyEn.save();
      console.log('✅ 隐私政策（英文）创建成功');
    } else {
      console.log('⚠️  隐私政策（英文）已存在，跳过创建');
    }

    console.log('\n🎉 所有文档创建完成！');
    console.log('\n📋 创建的文档：');
    console.log('- 服务条款（中文）- 分类：常见问题');
    console.log('- Terms of Service（英文）- 分类：常见问题');
    console.log('- 隐私政策（中文）- 分类：常见问题');
    console.log('- Privacy Policy（英文）- 分类：常见问题');
    console.log('\n💡 接下来需要：');
    console.log('1. 修改前端路由配置');
    console.log('2. 将 /terms 和 /privacy 路由指向文档页面');
    console.log('3. 删除原有的 Terms.tsx 和 Privacy.tsx 页面');

  } catch (error) {
    console.error('❌ 创建文档失败:', error);
  }
};

// 主函数
const main = async () => {
  await connectDB();
  await createDocuments();
  await mongoose.connection.close();
  console.log('\n🔌 数据库连接已关闭');
};

// 执行脚本
main().catch(console.error);
