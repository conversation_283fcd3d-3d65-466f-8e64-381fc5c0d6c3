import axios from 'axios';
import { getAdminAuthHeader } from './admin';

// 创建带有基础URL的axios实例
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:4000/api';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

export interface AdminDocument {
  _id: string;
  title: string;
  content: string;
  category: 'guide' | 'faq' | 'announcement';
  language: 'zh' | 'en';
  isVisible: boolean;
  order: number;
  originalId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateDocumentData {
  title: string;
  content: string;
  category: 'guide' | 'faq' | 'announcement';
  language: 'zh' | 'en';
  isVisible?: boolean;
  order?: number;
}

export interface UpdateDocumentData {
  title?: string;
  content?: string;
  category?: 'guide' | 'faq' | 'announcement';
  language?: 'zh' | 'en';
  isVisible?: boolean;
  order?: number;
}

/**
 * 获取管理员文档列表
 */
export const getAdminDocuments = async (params?: {
  category?: string;
  language?: string;
  search?: string;
  page?: number;
  limit?: number;
}) => {
  try {
    const headers = getAdminAuthHeader();
    const response = await api.get('/admin/docs', { headers, params });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '获取文档列表失败');
    }
    throw error;
  }
};

/**
 * 获取文档详情（管理员）
 */
export const getAdminDocumentById = async (id: string) => {
  try {
    const headers = getAdminAuthHeader();
    const response = await api.get(`/admin/docs/${id}`, { headers });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '获取文档详情失败');
    }
    throw error;
  }
};

/**
 * 创建文档
 */
export const createDocument = async (data: CreateDocumentData) => {
  try {
    const headers = getAdminAuthHeader();
    const response = await api.post('/admin/docs', data, { headers });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '创建文档失败');
    }
    throw error;
  }
};

/**
 * 更新文档
 */
export const updateDocument = async (id: string, data: UpdateDocumentData) => {
  try {
    const headers = getAdminAuthHeader();
    const response = await api.put(`/admin/docs/${id}`, data, { headers });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '更新文档失败');
    }
    throw error;
  }
};

/**
 * 删除文档
 */
export const deleteDocument = async (id: string) => {
  try {
    const headers = getAdminAuthHeader();
    const response = await api.delete(`/admin/docs/${id}`, { headers });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '删除文档失败');
    }
    throw error;
  }
};

/**
 * 翻译文档
 */
export const translateDocument = async (id: string, targetLanguage: 'zh' | 'en') => {
  try {
    const headers = getAdminAuthHeader();
    const response = await api.post(`/admin/docs/${id}/translate`,
      { targetLanguage },
      { headers }
    );
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '翻译文档失败');
    }
    throw error;
  }
};

/**
 * 上传文档图片
 */
export const uploadDocumentImage = async (file: File) => {
  try {
    const formData = new FormData();
    formData.append('image', file);

    const headers = {
      ...getAdminAuthHeader(),
      'Content-Type': 'multipart/form-data'
    };

    const response = await api.post('/admin/docs/upload-image', formData, { headers });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '上传图片失败');
    }
    throw error;
  }
};

export default {
  getAdminDocuments,
  getAdminDocumentById,
  createDocument,
  updateDocument,
  deleteDocument,
  translateDocument,
  uploadDocumentImage
};
