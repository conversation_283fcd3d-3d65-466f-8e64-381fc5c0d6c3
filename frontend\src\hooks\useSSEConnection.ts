import { useState, useEffect, useRef } from 'react';
import { KLineData } from '../types/chartTypes';

/**
 * 连接状态类型
 */
type ConnectionStatus = 'disconnected' | 'connecting' | 'connected' | 'reconnecting' | 'error';

/**
 * SSE消息类型
 */
interface SSEMessage {
  type: 'connection' | 'kline-update' | 'prediction-update' | 'prediction-line-update' | 'database-update' | 'heartbeat';
  data: any;
  timestamp: number;
}

/**
 * SSE连接状态
 */
interface SSEConnectionState {
  connectionStatus: ConnectionStatus;
  klineData: KLineData[];
  predictionData: any;
  predictionLineData: any;
  databaseUpdateTrigger: number;
  error: string | null;
  lastMessage: SSEMessage | null;
}

/**
 * SSE连接单例管理器
 */
class SSEConnectionManager {
  private static instance: SSEConnectionManager;
  private eventSource: EventSource | null = null;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private reconnectAttempts = 0;
  private isManualDisconnect = false;
  private subscribers = new Set<(state: SSEConnectionState) => void>();
  private allEventSources = new Set<EventSource>(); // 跟踪所有创建的EventSource
  
  // 配置
  private readonly maxReconnectAttempts = 5;
  private readonly baseReconnectDelay = 1000;
  private readonly API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:4000/api';
  
  // 状态
  private state: SSEConnectionState = {
    connectionStatus: 'disconnected',
    klineData: [],
    predictionData: null,
    predictionLineData: null,
    databaseUpdateTrigger: 0,
    error: null,
    lastMessage: null
  };

  private constructor() {}

  public static getInstance(): SSEConnectionManager {
    if (!SSEConnectionManager.instance) {
      SSEConnectionManager.instance = new SSEConnectionManager();
    }
    return SSEConnectionManager.instance;
  }

  /**
   * 订阅状态变化
   */
  public subscribe(callback: (state: SSEConnectionState) => void): () => void {
    this.subscribers.add(callback);
    // 立即发送当前状态
    callback(this.state);
    
    // 返回取消订阅函数
    return () => {
      this.subscribers.delete(callback);
    };
  }

  /**
   * 通知所有订阅者
   */
  private notifySubscribers(): void {
    this.subscribers.forEach(callback => callback(this.state));
  }

  /**
   * 更新状态
   */
  private updateState(updates: Partial<SSEConnectionState>): void {
    this.state = { ...this.state, ...updates };
    this.notifySubscribers();
  }

  /**
   * 建立SSE连接
   */
  public connect(): void {
    // 如果已经在连接中或已连接，不重复连接
    if (this.state.connectionStatus === 'connecting' || this.state.connectionStatus === 'connected') {
      console.log('SSE连接正在进行中或已连接，跳过重复连接');
      return;
    }

    // 如果已达到最大重连次数，不再连接
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('已达到最大重连次数，不再尝试连接');
      return;
    }

    // 关闭所有现有的EventSource连接
    if (this.allEventSources.size > 0) {
      console.log(`关闭 ${this.allEventSources.size} 个现有的EventSource连接`);
      this.allEventSources.forEach(es => {
        try {
          es.close();
        } catch (e) {
          console.warn('关闭EventSource时出错:', e);
        }
      });
      this.allEventSources.clear();
    }

    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }

    this.updateState({ connectionStatus: 'connecting', error: null });
    this.isManualDisconnect = false;

    try {
      const token = localStorage.getItem('accessToken');
      const url = token 
        ? `${this.API_BASE_URL}/kline-sse/stream?token=${encodeURIComponent(token)}`
        : `${this.API_BASE_URL}/kline-sse/stream`;

      console.log('建立统一SSE连接:', url);

      const eventSource = new EventSource(url);
      this.eventSource = eventSource;
      this.allEventSources.add(eventSource); // 跟踪这个EventSource

      eventSource.onopen = () => {
        // 检查这个EventSource是否还是当前活跃的连接
        if (this.eventSource !== eventSource) {
          console.log('收到旧EventSource的连接成功事件，忽略');
          return;
        }

        console.log('统一SSE连接已建立');
        this.updateState({ connectionStatus: 'connected', error: null });
        this.reconnectAttempts = 0;
      };

      eventSource.onmessage = (event) => {
        try {
          const message: SSEMessage = JSON.parse(event.data);
          
          const updates: Partial<SSEConnectionState> = { lastMessage: message };

          switch (message.type) {
            case 'connection':
              console.log('SSE连接确认:', message.data);
              break;
              
            case 'kline-update':
              console.log('收到K线数据更新:', message.data.length, '条数据');
              updates.klineData = message.data;
              break;

            // 预测数据推送已移除，改为仅通过数据库更新通知
              
            case 'database-update':
              console.log('收到数据库更新通知:', message.data);
              updates.databaseUpdateTrigger = this.state.databaseUpdateTrigger + 1;
              break;

            case 'heartbeat':
              console.debug('收到心跳消息');
              break;

            default:
              console.log('收到未知类型SSE消息:', message.type);
          }

          this.updateState(updates);
        } catch (error) {
          console.error('解析SSE消息失败:', error, event.data);
        }
      };

      eventSource.onerror = () => {
        // 检查这个EventSource是否还是当前活跃的连接
        if (this.eventSource !== eventSource) {
          console.log('收到旧EventSource的错误事件，忽略');
          return;
        }

        console.error('连接失败，请检查网络或刷新页面');
        this.updateState({ connectionStatus: 'error', error: '连接失败，请检查网络或刷新页面' });

        if (!this.isManualDisconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
          const delay = this.baseReconnectDelay * Math.pow(2, this.reconnectAttempts);
          console.log(`${delay}ms后尝试重连 (第${this.reconnectAttempts + 1}次)`);

          this.updateState({ connectionStatus: 'reconnecting' });
          this.reconnectTimeout = setTimeout(() => {
            this.reconnectAttempts++;
            this.connect();
          }, delay);
        } else {
          console.error('达到最大重连次数，停止重连');
          this.updateState({
            connectionStatus: 'error',
            error: '连接失败，请检查网络或刷新页面'
          });
          // 清理所有EventSource，防止继续触发事件
          this.allEventSources.forEach(es => {
            try {
              es.close();
            } catch (e) {
              console.warn('清理EventSource时出错:', e);
            }
          });
          this.allEventSources.clear();
          this.eventSource = null;
        }
      };

    } catch (error) {
      console.error('创建SSE连接失败:', error);
      this.updateState({ connectionStatus: 'error', error: '创建SSE连接失败' });
    }
  }

  /**
   * 断开SSE连接
   */
  public disconnect(): void {
    this.isManualDisconnect = true;

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    // 关闭所有EventSource连接
    if (this.allEventSources.size > 0) {
      console.log(`断开时关闭 ${this.allEventSources.size} 个EventSource连接`);
      this.allEventSources.forEach(es => {
        try {
          es.close();
        } catch (e) {
          console.warn('断开EventSource时出错:', e);
        }
      });
      this.allEventSources.clear();
    }

    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }

    this.updateState({ connectionStatus: 'disconnected', error: null });
    console.log('统一SSE连接已断开');
  }

  /**
   * 检查是否可以重连
   */
  public canReconnect(): boolean {
    return !this.isManualDisconnect &&
           this.reconnectAttempts < this.maxReconnectAttempts;
  }

  /**
   * 重新连接
   */
  public reconnect(): void {
    this.disconnect();
    setTimeout(() => {
      this.reconnectAttempts = 0;
      this.connect();
    }, 1000);
  }

  /**
   * 获取当前状态
   */
  public getState(): SSEConnectionState {
    return this.state;
  }

  /**
   * 获取订阅者数量
   */
  public getSubscriberCount(): number {
    return this.subscribers.size;
  }
}

/**
 * SSE连接Hook
 * 使用单例管理器确保整个应用只有一个SSE连接
 */
export const useSSEConnection = () => {
  const [state, setState] = useState<SSEConnectionState>(() => 
    SSEConnectionManager.getInstance().getState()
  );
  
  const managerRef = useRef(SSEConnectionManager.getInstance());

  useEffect(() => {
    const manager = managerRef.current;

    // 订阅状态变化
    const unsubscribe = manager.subscribe(setState);

    // 如果是第一个订阅者，建立连接
    if (manager.getSubscriberCount() === 1) {
      console.log('首个SSE订阅者，建立连接');
      // 延迟一点时间，避免React.StrictMode导致的重复调用
      setTimeout(() => {
        if (manager.getSubscriberCount() > 0) {
          manager.connect();
        }
      }, 100);
    }

    return () => {
      unsubscribe();
      // 如果没有订阅者了，断开连接
      if (manager.getSubscriberCount() === 0) {
        console.log('所有SSE订阅者已取消，断开连接');
        manager.disconnect();
      }
    };
  }, []);

  // 页面可见性变化处理 - 暂时禁用自动重连，避免无限循环
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        console.log('页面隐藏，保持SSE连接');
      } else {
        console.log('页面显示，SSE连接状态:', state.connectionStatus);
        // 暂时禁用页面可见性变化时的自动重连，避免无限循环
        // 用户可以手动刷新页面来重新建立连接
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [state.connectionStatus]);

  return {
    // 状态
    connectionStatus: state.connectionStatus,
    klineData: state.klineData,
    predictionData: state.predictionData,
    predictionLineData: state.predictionLineData,
    error: state.error,
    lastMessage: state.lastMessage,
    databaseUpdateTrigger: state.databaseUpdateTrigger,
    
    // 方法
    connect: () => managerRef.current.connect(),
    disconnect: () => managerRef.current.disconnect(),
    reconnect: () => managerRef.current.reconnect(),
    
    // 计算属性
    isConnected: state.connectionStatus === 'connected',
    isConnecting: state.connectionStatus === 'connecting' || state.connectionStatus === 'reconnecting',
    hasError: state.connectionStatus === 'error',
    hasKlineData: state.klineData.length > 0,
    hasPredictionData: !!state.predictionData,
    hasPredictionLineData: !!state.predictionLineData
  };
};
