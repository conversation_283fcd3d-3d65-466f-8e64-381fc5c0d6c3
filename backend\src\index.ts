import express from 'express';
import mongoose from 'mongoose';
import cors from 'cors';
import cookieParser from 'cookie-parser';
import path from 'path';
import fs from 'fs';
import config from './config';
import routes from './routes';
import scheduler from './utils/scheduler';
import predictionService from './services/predictionService';
import Prediction from './models/Prediction';
import configService from './services/configService';
import logger from './utils/logger';

/**
 * BTC预测系统后端主入口文件
 * 负责初始化Express应用，连接数据库，启动API服务和定时任务
 */

// 创建Express应用实例
const app = express();

// 配置中间件
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',  // 明确指定前端URL
  credentials: true            // 允许跨域请求携带凭证
}));
app.use(express.json());                         // 处理JSON请求体
app.use(express.urlencoded({ extended: true })); // 处理表单数据
app.use(cookieParser());                         // 解析Cookie

// 配置静态文件服务
const publicPath = path.join(__dirname, '..', 'public');
app.use(express.static(publicPath));
logger.info('静态文件目录', { publicPath });

// 注册API路由
app.use('/api', routes);

/**
 * 连接MongoDB数据库
 * 使用配置文件中的URI连接MongoDB
 */
const connectDB = async () => {
  try {
    await mongoose.connect(config.mongoUri);
    logger.info('MongoDB连接成功');

    // 初始化数据库，清理旧索引
    await initializeDatabase();
  } catch (error) {
    logger.error('MongoDB连接错误', error);
    process.exit(1); // 数据库连接失败时退出应用
  }
};

/**
 * 初始化数据库
 * 清理不再使用的旧索引，确保数据结构正确
 */
async function initializeDatabase() {
  try {
    // 获取Prediction集合
    const collection = mongoose.connection.collection('predictions');

    // 获取所有索引
    const indexes = await collection.indexes();
    logger.info('当前索引列表', { indexCount: indexes.length });

    // 查找并删除旧的sourceTimestamp/targetTimestamp索引
    for (const index of indexes) {
      if (index.name && (
          index.name.includes('sourceTimestamp') ||
          index.name.includes('targetTimestamp')
      )) {
        logger.info(`删除旧索引: ${index.name}`);
        await collection.dropIndex(index.name);
      }
    }

    // 确保targetStartTime索引存在
    const existingIndexes = await collection.indexes();
    const hasTargetStartIndex = existingIndexes.some(idx =>
      idx.name && idx.name.includes('targetStartTime')
    );

    if (!hasTargetStartIndex) {
      logger.info('创建symbol和targetStartTime的复合索引');
      await collection.createIndex(
        { symbol: 1, targetStartTime: 1 },
        { unique: true }
      );
    }

    logger.info('数据库初始化完成');
  } catch (error) {
    logger.error('初始化数据库时出错', error);
  }
}

/**
 * 启动服务器
 * 连接数据库、启动定时任务并监听端口
 */
const startServer = async () => {
  // 连接数据库
  await connectDB();

  // 初始化配置服务
  try {
    logger.info('初始化配置服务...');
    await configService.init();
    logger.info('配置服务初始化成功');
  } catch (error) {
    logger.error('初始化配置服务出错', error);
    // 继续启动服务，不因为配置服务初始化失败而中断启动流程
  }

  // 服务器启动时检查并补齐预测K线
  try {
    logger.info('服务器启动时检查预测K线数据完整性...');
    const filledPredictions = await predictionService.checkAndFillMissingPredictions();
    logger.info(`启动检查完成，已补齐${filledPredictions.length}条预测记录`);
  } catch (error) {
    logger.error('启动时检查预测数据出错', error);
    // 继续启动服务，不因为检查失败而中断启动流程
  }

  // 服务器启动时检查并补齐30分钟K线数据
  try {
    logger.info('服务器启动时检查30分钟K线数据完整性...');
    const thirtyMinKlineService = (await import('./services/thirtyMinKlineService')).default;
    const fixedCount = await thirtyMinKlineService.checkAndFillHistoricalData();
    logger.info(`30分钟K线数据检查完成，已补齐${fixedCount}条记录`);
  } catch (error) {
    logger.error('启动时检查30分钟K线数据出错', error);
    // 继续启动服务，不因为检查失败而中断启动流程
  }

  // 确保上传目录存在
  const uploadDir = path.join(__dirname, '..', 'public', 'uploads');
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
    logger.info('创建上传目录', { uploadDir });
  }

  // 启动定时任务
  scheduler.startAllJobs();

  // 启动K线SSE数据刷新服务
  try {
    logger.info('启动K线SSE数据刷新服务...');
    const klineDataRefreshService = (await import('./services/klineDataRefreshService')).default;
    klineDataRefreshService.start();
    logger.info('K线SSE数据刷新服务启动成功');
  } catch (error) {
    logger.error('启动K线SSE数据刷新服务失败', error);
    // 继续启动服务，不因为SSE服务启动失败而中断启动流程
  }

  // 监听端口
  app.listen(config.port, () => {
    logger.info(`服务器运行在端口 ${config.port}`);
  });
};

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  logger.error('未捕获的异常', error);
});

// 处理未处理的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
  logger.error('未处理的Promise拒绝', { reason, promise });
});

// 启动应用
startServer();