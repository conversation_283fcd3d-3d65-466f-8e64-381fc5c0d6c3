import React, { Component, ErrorInfo, ReactNode, useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import { useTokenRefresh } from './hooks/useTokenRefresh';
import './App.css';
import './i18n';
import { useTranslation } from 'react-i18next';
import LandingPage from './pages/LandingPage';
import Login from './pages/Login';
import Register from './pages/Register';
import EmailVerification from './pages/EmailVerification';
// VerifySuccess页面已移除，现在使用验证码验证方式
import ForgotPassword from './pages/ForgotPassword';
import ResetPassword from './pages/ResetPassword';
import PrivacyRedirect from './pages/PrivacyRedirect';
import TermsRedirect from './pages/TermsRedirect';
import UserCenter from './pages/UserCenter';
import Dashboard from './pages/Dashboard';
import Subscribe from './pages/Subscribe';
import SubscriptionSuccess from './pages/SubscriptionSuccess';
import NotFound from './pages/NotFound';
import PrivateRoute from './routes/PrivateRoute';
import AdminRoute from './routes/AdminRoute';
import useUserStore from './store/useUserStore';
import useAdminStore from './store/useAdminStore';
import PublicLayout from './layouts/PublicLayout';
import AuthLayout from './layouts/AuthLayout';
import AppLayout from './layouts/AppLayout';
import SubscriptionLayout from './layouts/SubscriptionLayout';
import DocsLayout from './layouts/DocsLayout';
import { Toaster } from './components/ui/toaster';
import tokenService from './api/tokenService';
import configService from './services/configService';

// 管理员页面
import AdminLogin from './pages/admin/Login';
import AdminDashboard from './pages/admin/Dashboard';
import AdminUsers from './pages/admin/Users';

import AdminPayments from './pages/admin/Payments';
import AdminPaymentDetails from './pages/admin/PaymentDetails';
import AdminFeedback from './pages/admin/Feedback';
import AdminFeedbackDetail from './pages/admin/FeedbackDetail';
import Documents from './pages/admin/Documents';
import DocumentEditor from './pages/admin/DocumentEditor';
import SettingsIndex from './pages/admin/settings/SettingsIndex';
import BasicSettings from './pages/admin/settings/BasicSettings';
import UserSettings from './pages/admin/settings/UserSettings';
import PaymentSettings from './pages/admin/settings/PaymentSettings';
import SecuritySettings from './pages/admin/settings/SecuritySettings';
import TranslationSettings from './pages/admin/settings/TranslationSettings';
import EmailSettings from './pages/admin/settings/EmailSettings';
import LogSettings from './pages/admin/settings/LogSettings';

// 管理员工具页面
import DataCleanup from './pages/admin/tools/DataCleanup';
import SolarTerms from './pages/admin/tools/SolarTerms';

// 管理员系统页面
import SystemLogs from './pages/admin/system/Logs';

// 文档页面
import DocsPage from './pages/Docs/DocsPage';

// 错误边界组件
class ErrorBoundary extends Component<{ children: ReactNode }, { hasError: boolean }> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(_: Error) {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error:', error);
    console.error('Error Info:', errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-background text-content-primary">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">出错了</h1>
            <p className="text-content-secondary">请刷新页面重试</p>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

function AppContent() {
  const { fetchUserInfo, isAuthenticated } = useUserStore();
  const { initialize } = useAdminStore();

  // 应用启动时根据路径选择性初始化
  useEffect(() => {
    // 首先尝试从localStorage加载令牌到内存中
    tokenService.loadTokenFromStorage();

    // 根据路径判断应该初始化哪种角色
    const pathname = window.location.pathname;
    const isAdminPath = pathname.startsWith('/admin');
    const isAdminLoginPage = pathname === '/admin/login';

    // 为了防止循环，检查是否有正在进行的重定向
    const isRedirecting = sessionStorage.getItem('admin_redirecting') === 'true';

    if (isRedirecting) {
      console.log('检测到重定向状态，跳过初始化');
      sessionStorage.removeItem('admin_redirecting');
      return;
    }

    // 非管理员路径或首页，只初始化普通用户信息
    if (!isAdminPath || pathname === '/') {
      console.log('初始化普通用户信息');
    fetchUserInfo();
    }

    // 管理员路径且不是登录页，初始化管理员信息
    if (isAdminPath && !isAdminLoginPage) {
      console.log('初始化管理员信息');
    initialize();
      // 管理员页面仍需要基础用户信息，但避免在初始化失败后循环
      const adminStorage = localStorage.getItem('admin-storage');
      if (adminStorage) {
        try {
          const parsed = JSON.parse(adminStorage);
          if (parsed.state && parsed.state.token) {
            fetchUserInfo();
          } else {
            console.log('未找到管理员令牌，跳过用户信息获取');
          }
        } catch {
          console.warn('解析管理员存储失败，跳过用户信息获取');
        }
      } else {
        console.log('未找到管理员存储，跳过用户信息获取');
      }
    }

    // 如果是管理员登录页，不需要初始化任何信息
    if (isAdminLoginPage) {
      console.log('管理员登录页，跳过自动认证');
    }
  }, [fetchUserInfo, initialize]);

  return (
    <Routes>
      {/* 公开路由 */}
      <Route element={<PublicLayout />}>
        <Route path="/" element={
          isAuthenticated ? <Navigate to="/dashboard" replace /> : <LandingPage />
        } />
        {/* verify-email路由已移除，现在使用验证码验证方式 */}
        <Route path="privacy" element={<PrivacyRedirect />} />
        <Route path="terms" element={<TermsRedirect />} />
      </Route>

      {/* 认证路由 - 独立布局 */}
      <Route element={<AuthLayout />}>
        <Route path="login" element={<Login />} />
        <Route path="register" element={<Register />} />
        <Route path="email-verification" element={<EmailVerification />} />
        <Route path="forgot-password" element={<ForgotPassword />} />
        <Route path="reset-password" element={<ResetPassword />} />
      </Route>

      {/* 文档路由 */}
      <Route element={<DocsLayout />}>
        <Route path="docs" element={<DocsPage />} />
        <Route path="docs/article/:id" element={<DocsPage />} />
        <Route path="faq" element={<DocsPage />} />
        <Route path="faq/article/:id" element={<DocsPage />} />
        <Route path="announcement" element={<DocsPage />} />
        <Route path="announcement/article/:id" element={<DocsPage />} />
      </Route>

      {/* 受保护路由 */}
      <Route element={<PrivateRoute />}>
        <Route element={<AppLayout />}>
          <Route path="dashboard" element={<Dashboard />} />
          <Route path="user-center" element={<UserCenter />} />
          <Route path="subscribe" element={<Subscribe />} />
        </Route>

        {/* 订阅流程页面 */}
        <Route element={<SubscriptionLayout />}>
          <Route path="subscription/cancel" element={<Navigate to="/subscribe" replace />} />
        </Route>

        {/* 订阅成功页面 - 独立布局，无导航栏 */}
        <Route path="subscription/success" element={<SubscriptionSuccess />} />
      </Route>

      {/* 管理员路由 */}
      <Route path="/admin/login" element={<AdminLogin />} />
      <Route path="/admin" element={<AdminRoute />}>
        <Route path="dashboard" element={<AdminDashboard />} />
        <Route path="users" element={<AdminUsers />} />

        <Route path="settings" element={<SettingsIndex />} />
        <Route path="settings/basic" element={<BasicSettings />} />
        <Route path="settings/user" element={<UserSettings />} />
        <Route path="settings/payment" element={<PaymentSettings />} />
        <Route path="settings/security" element={<SecuritySettings />} />
        <Route path="settings/translation" element={<TranslationSettings />} />
        <Route path="settings/email" element={<EmailSettings />} />
        <Route path="settings/logs" element={<LogSettings />} />
        <Route path="system/logs" element={<SystemLogs />} />
        <Route path="payments" element={<AdminPayments />} />
        <Route path="payments/:id" element={<AdminPaymentDetails />} />
        <Route path="feedback" element={<AdminFeedback />} />
        <Route path="feedback/:id" element={<AdminFeedbackDetail />} />
        <Route path="documents" element={<Documents />} />
        <Route path="documents/create" element={<DocumentEditor />} />
        <Route path="documents/edit/:id" element={<DocumentEditor />} />

        {/* 管理员工具路由 */}
        <Route path="tools/data-cleanup" element={<DataCleanup />} />
        <Route path="tools/solar-terms" element={<SolarTerms />} />
      </Route>

      {/* 404 页面 - 必须放在最后，匹配所有未定义的路由 */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

// 创建一个高阶组件来处理身份验证
const AuthWrapper = ({ children }: { children: React.ReactNode }) => {
  const navigate = useNavigate();
  const location = useLocation();

  // 使用token刷新hook
  useTokenRefresh();

  // 全局错误处理
  useEffect(() => {
    const handleUnauthorized = (event: CustomEvent) => {
      // 如果当前不在登录页，重定向到登录页
      if (!location.pathname.includes('/login')) {
        navigate('/login', { state: { reason: 'session_expired', from: location.pathname } });
      }
    };

    // 创建自定义事件监听
    window.addEventListener('unauthorized', handleUnauthorized as EventListener);

    return () => {
      window.removeEventListener('unauthorized', handleUnauthorized as EventListener);
    };
  }, [navigate, location]);

  return <>{children}</>;
};

function App() {
  const [isConfigLoaded, setIsConfigLoaded] = useState(false);
  const { i18n } = useTranslation();

  // 设置默认标题（在配置加载之前）
  useEffect(() => {
    // 如果当前标题还是默认的React App，先设置一个临时标题
    if (document.title === 'React App' || document.title === 'BTC 预测') {
      document.title = 'BTC 预测';
    }
  }, []);

  // 加载网站配置
  useEffect(() => {
    const loadConfig = async () => {
      try {
        // 获取网站配置
        const config = await configService.getConfig();

        // 更新网站标题
        document.title = config.siteInfo.siteName;

        // 更新网站描述
        const metaDescription = document.querySelector('meta[name="description"]');
        if (metaDescription) {
          metaDescription.setAttribute('content', config.siteInfo.siteDescription);
        }

        // 更新网站关键词
        let metaKeywords = document.querySelector('meta[name="keywords"]');
        if (!metaKeywords) {
          metaKeywords = document.createElement('meta');
          metaKeywords.setAttribute('name', 'keywords');
          document.head.appendChild(metaKeywords);
        }
        metaKeywords.setAttribute('content', config.siteInfo.siteKeywords);



        // 处理语言设置
        const savedLanguage = localStorage.getItem('i18nextLng');
        const userChosenLanguage = localStorage.getItem('userChosenLanguage'); // 用户主动选择的标记

        if (!userChosenLanguage) {
          // 首次访问用户或浏览器自动检测的语言，使用管理员设置的默认语言
          console.log('首次访问用户，应用管理员设置的默认语言:', config.siteInfo.defaultLanguage);
          await i18n.changeLanguage(config.siteInfo.defaultLanguage);
          // 标记这是管理员设置的默认语言，不是用户选择
          localStorage.setItem('userChosenLanguage', 'false');
        } else if (userChosenLanguage === 'true' && savedLanguage) {
          // 用户之前主动选择过语言，保留其选择
          console.log('用户之前主动选择的语言，保留选择:', savedLanguage);
        } else {
          // 其他情况，使用管理员设置的默认语言
          console.log('应用管理员设置的默认语言:', config.siteInfo.defaultLanguage);
          await i18n.changeLanguage(config.siteInfo.defaultLanguage);
        }

        setIsConfigLoaded(true);
      } catch (error) {
        console.error('加载网站配置失败:', error);
        // 即使配置加载失败，也允许应用继续运行
        setIsConfigLoaded(true);
      }
    };

    loadConfig();
  }, [i18n]);

  // 显示加载指示器
  if (!isConfigLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="App">
      <ErrorBoundary>
        <Router>
          <Routes>
            <Route path="/*" element={
              <AuthWrapper>
                <AppContent />
              </AuthWrapper>
            } />
          </Routes>
          <Toaster />
        </Router>
      </ErrorBoundary>
    </div>
  );
}

export default App;
