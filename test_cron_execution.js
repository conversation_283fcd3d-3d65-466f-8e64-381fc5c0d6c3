// 测试cron任务执行时机问题

// 模拟调度器逻辑
function simulateScheduler() {
  const now = new Date();
  const minute = now.getUTCMinutes();
  const hour = now.getUTCHours();
  const isOddHour = hour % 2 === 1;

  console.log(`[模拟调度器][${now.toISOString()}] 检查时间: ${hour}:${minute.toString().padStart(2, '0')}`);

  const shouldTrigger = isOddHour
    ? [2, 28, 58].includes(minute)
    : [28].includes(minute);

  if (shouldTrigger) {
    console.log(`[模拟调度器][${now.toISOString()}] 触发预测任务，小时: ${hour}, 分钟: ${minute}`);
    
    // 模拟预测服务验证
    const paddedMinute = minute.toString().padStart(2, '0');
    const isValid = isOddHour
      ? ['02', '28', '58'].includes(paddedMinute)
      : ['28'].includes(paddedMinute);
    
    console.log(`[模拟预测服务] 时间验证结果: ${isValid}`);
    
    if (isValid) {
      console.log(`[模拟预测服务] 开始生成预测...`);
    } else {
      console.log(`[模拟预测服务] ❌ 时间验证失败！`);
    }
  } else {
    console.log(`[模拟调度器] 不是触发时间，跳过`);
  }
}

// 测试特定时间点
const testTimes = [
  '2025-07-23T19:28:00.000Z',
  '2025-07-23T19:58:00.000Z',
  '2025-07-23T20:28:00.000Z',
  '2025-07-23T19:57:00.000Z', // 19:58前一分钟
  '2025-07-23T19:59:00.000Z', // 19:58后一分钟
];

console.log('测试特定时间点的调度器行为：\n');

testTimes.forEach(timeStr => {
  // 临时修改系统时间（仅用于测试）
  const originalNow = Date.now;
  Date.now = () => new Date(timeStr).getTime();
  global.Date = class extends Date {
    constructor(...args) {
      if (args.length === 0) {
        super(timeStr);
      } else {
        super(...args);
      }
    }
  };
  
  console.log(`\n=== 测试时间: ${timeStr} ===`);
  simulateScheduler();
  
  // 恢复原始时间
  Date.now = originalNow;
});

// 测试cron表达式的边界情况
console.log('\n\n测试cron执行的边界情况：');

// 模拟cron可能在58秒或59秒执行的情况
const boundaryTimes = [
  '2025-07-23T19:57:58.000Z', // 57分58秒
  '2025-07-23T19:57:59.000Z', // 57分59秒
  '2025-07-23T19:58:00.000Z', // 58分00秒
  '2025-07-23T19:58:01.000Z', // 58分01秒
  '2025-07-23T19:58:58.000Z', // 58分58秒
  '2025-07-23T19:58:59.000Z', // 58分59秒
  '2025-07-23T19:59:00.000Z', // 59分00秒
];

boundaryTimes.forEach(timeStr => {
  const testDate = new Date(timeStr);
  const minute = testDate.getUTCMinutes();
  const second = testDate.getUTCSeconds();
  
  console.log(`时间: ${timeStr} (${minute}分${second}秒) - 分钟值: ${minute}`);
});
