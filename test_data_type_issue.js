// 测试数据类型不匹配问题

// 调度器逻辑
function schedulerCheck(hour, minute) {
  const isOddHour = hour % 2 === 1;
  return isOddHour
    ? [2, 28, 58].includes(minute)
    : [28].includes(minute);
}

// 预测服务逻辑
function predictionServiceCheck(hour, minute) {
  const isOddHour = hour % 2 === 1;
  const paddedMinute = minute.toString().padStart(2, '0');
  
  if (isOddHour) {
    return ['02', '28', '58'].includes(paddedMinute);
  } else {
    return ['28'].includes(paddedMinute);
  }
}

// 测试所有可能的情况
console.log('测试数据类型匹配问题：\n');

const testCases = [
  { hour: 19, minute: 2, desc: '19:02' },
  { hour: 19, minute: 28, desc: '19:28' },
  { hour: 19, minute: 58, desc: '19:58' },
  { hour: 20, minute: 28, desc: '20:28' },
];

testCases.forEach(test => {
  const schedulerResult = schedulerCheck(test.hour, test.minute);
  const predictionResult = predictionServiceCheck(test.hour, test.minute);
  
  console.log(`${test.desc}:`);
  console.log(`  调度器: ${schedulerResult}`);
  console.log(`  预测服务: ${predictionResult}`);
  console.log(`  匹配: ${schedulerResult === predictionResult ? '✅' : '❌'}`);
  console.log('');
});

// 测试边界情况 - 如果minute是字符串类型
console.log('测试边界情况（分钟为字符串）：\n');

const edgeCases = [
  { hour: 19, minute: '2', desc: '19:"2"' },
  { hour: 19, minute: '02', desc: '19:"02"' },
  { hour: 19, minute: '58', desc: '19:"58"' },
];

edgeCases.forEach(test => {
  const schedulerResult = schedulerCheck(test.hour, test.minute);
  const predictionResult = predictionServiceCheck(test.hour, test.minute);
  
  console.log(`${test.desc}:`);
  console.log(`  调度器: ${schedulerResult}`);
  console.log(`  预测服务: ${predictionResult}`);
  console.log(`  匹配: ${schedulerResult === predictionResult ? '✅' : '❌'}`);
  console.log('');
});

// 测试实际的Date对象
console.log('测试实际Date对象：\n');

const testDate = new Date('2025-07-23T19:58:00.000Z');
console.log(`测试时间: ${testDate.toISOString()}`);
console.log(`UTC小时: ${testDate.getUTCHours()}`);
console.log(`UTC分钟: ${testDate.getUTCMinutes()}`);
console.log(`分钟类型: ${typeof testDate.getUTCMinutes()}`);

const hour = testDate.getUTCHours();
const minute = testDate.getUTCMinutes();

console.log(`调度器判断: ${schedulerCheck(hour, minute)}`);
console.log(`预测服务判断: ${predictionServiceCheck(hour, minute)}`);
