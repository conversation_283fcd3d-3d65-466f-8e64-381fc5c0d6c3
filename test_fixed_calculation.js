// 测试修复后的时间计算逻辑

// 修复后的calculateTimes逻辑
function calculateTimesFixed(currentTime) {
  const hour = currentTime.getUTCHours();
  const minute = currentTime.getUTCMinutes();
  const isOddHour = hour % 2 === 1;

  let sourceKlineTime;
  let targetStartTime;
  let targetEndTime;

  let workDate = new Date(currentTime);
  workDate.setUTCSeconds(0);
  workDate.setUTCMilliseconds(0);

  if (isOddHour) {
    if (minute === 2) {
      // 奇数小时02分：使用HH:00的K线预测HH:00-HH:30
      workDate.setUTCMinutes(0);
      sourceKlineTime = workDate.getTime();
      targetStartTime = sourceKlineTime;
      
      // 重新创建日期对象设置结束时间
      workDate = new Date(currentTime);
      workDate.setUTCSeconds(0);
      workDate.setUTCMilliseconds(0);
      workDate.setUTCMinutes(30);
      targetEndTime = workDate.getTime();
    } else if (minute === 28) {
      // 奇数小时28分：使用HH:01的K线预测HH:30-HH+1:00
      workDate.setUTCMinutes(1);
      sourceKlineTime = workDate.getTime();
      
      // 重新创建日期对象设置开始时间
      workDate = new Date(currentTime);
      workDate.setUTCSeconds(0);
      workDate.setUTCMilliseconds(0);
      workDate.setUTCMinutes(30);
      targetStartTime = workDate.getTime();
      
      // 重新创建日期对象设置结束时间
      workDate = new Date(currentTime);
      workDate.setUTCSeconds(0);
      workDate.setUTCMilliseconds(0);
      workDate.setUTCHours(hour + 1);
      workDate.setUTCMinutes(0);
      targetEndTime = workDate.getTime();
    } else if (minute === 58) {
      // 奇数小时58分：使用HH:02的K线预测HH+1:00-HH+1:30
      workDate.setUTCMinutes(2);
      sourceKlineTime = workDate.getTime();
      
      // 重新创建日期对象并设置为下一小时，确保时间计算的独立性
      workDate = new Date(currentTime);
      workDate.setUTCSeconds(0);
      workDate.setUTCMilliseconds(0);
      workDate.setUTCHours(hour + 1);
      workDate.setUTCMinutes(0);
      targetStartTime = workDate.getTime();
      
      // 重新创建日期对象并设置为下一小时30分
      workDate = new Date(currentTime);
      workDate.setUTCSeconds(0);
      workDate.setUTCMilliseconds(0);
      workDate.setUTCHours(hour + 1);
      workDate.setUTCMinutes(30);
      targetEndTime = workDate.getTime();
    }
  } else {
    if (minute === 28) {
      // 偶数小时28分的逻辑保持不变
      if (hour === 0) {
        const prevDay = new Date(workDate.getTime());
        prevDay.setUTCDate(prevDay.getUTCDate() - 1);
        prevDay.setUTCHours(23);
        prevDay.setUTCMinutes(3);
        sourceKlineTime = prevDay.getTime();
      } else {
        workDate.setUTCHours(hour - 1);
        workDate.setUTCMinutes(3);
        sourceKlineTime = workDate.getTime();
      }
      
      workDate = new Date(currentTime);
      workDate.setUTCSeconds(0);
      workDate.setUTCMilliseconds(0);
      workDate.setUTCMinutes(30);
      targetStartTime = workDate.getTime();
      
      workDate.setUTCHours(hour + 1);
      workDate.setUTCMinutes(0);
      targetEndTime = workDate.getTime();
    }
  }

  return { sourceKlineTime, targetStartTime, targetEndTime };
}

// generateExpectedPredictionTimes中对应的逻辑（58分部分）
function generateExpectedLogic(checkTime) {
  const hour = checkTime.getUTCHours();
  const minute = checkTime.getUTCMinutes();
  
  let workDate = new Date(checkTime);
  let predictionSourceKlineTime = 0;
  let predictionTargetStartTime = 0;
  let predictionTargetEndTime = 0;
  
  if (minute === 58) {
    // 奇数小时58分：使用HH:02的K线预测HH+1:00-HH+1:30
    workDate.setUTCMinutes(2);
    predictionSourceKlineTime = workDate.getTime();
    
    // 重新创建日期对象并设置为下一小时
    workDate = new Date(checkTime);
    workDate.setUTCHours(hour + 1);
    workDate.setUTCMinutes(0);
    predictionTargetStartTime = workDate.getTime();
    
    // 重新创建日期对象并设置为下一小时30分
    workDate = new Date(checkTime);
    workDate.setUTCHours(hour + 1);
    workDate.setUTCMinutes(30);
    predictionTargetEndTime = workDate.getTime();
  }
  
  return {
    sourceKlineTime: predictionSourceKlineTime,
    targetStartTime: predictionTargetStartTime,
    targetEndTime: predictionTargetEndTime
  };
}

// 测试修复后的一致性
console.log('测试修复后的时间计算一致性：\n');

const testTime = new Date('2025-07-23T19:58:00.000Z');
console.log(`测试时间: ${testTime.toISOString()}\n`);

const fixedResult = calculateTimesFixed(testTime);
const expectedResult = generateExpectedLogic(testTime);

console.log('修复后的calculateTimes结果:');
console.log(`源K线时间: ${new Date(fixedResult.sourceKlineTime).toISOString()}`);
console.log(`目标开始时间: ${new Date(fixedResult.targetStartTime).toISOString()}`);
console.log(`目标结束时间: ${new Date(fixedResult.targetEndTime).toISOString()}`);

console.log('\ngenerateExpectedPredictionTimes结果:');
console.log(`源K线时间: ${new Date(expectedResult.sourceKlineTime).toISOString()}`);
console.log(`目标开始时间: ${new Date(expectedResult.targetStartTime).toISOString()}`);
console.log(`目标结束时间: ${new Date(expectedResult.targetEndTime).toISOString()}`);

console.log('\n一致性检查:');
console.log(`源K线时间一致: ${fixedResult.sourceKlineTime === expectedResult.sourceKlineTime ? '✅' : '❌'}`);
console.log(`目标开始时间一致: ${fixedResult.targetStartTime === expectedResult.targetStartTime ? '✅' : '❌'}`);
console.log(`目标结束时间一致: ${fixedResult.targetEndTime === expectedResult.targetEndTime ? '✅' : '❌'}`);

const allConsistent = 
  fixedResult.sourceKlineTime === expectedResult.sourceKlineTime &&
  fixedResult.targetStartTime === expectedResult.targetStartTime &&
  fixedResult.targetEndTime === expectedResult.targetEndTime;

console.log(`\n总体一致性: ${allConsistent ? '✅ 完全一致' : '❌ 存在差异'}`);
