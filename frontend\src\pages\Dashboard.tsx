import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import MainKlineChart from '../components/MainKlineChart';
import useUserStore from '../store/useUserStore';
import { getSubscriptionInfo, SubscriptionInfo } from '../api/subscription';
import { Button } from '../components/ui/button';



// 仪表盘组件
const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user, fetchUserInfo } = useUserStore();
  const [subscription, setSubscription] = useState<SubscriptionInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 检查用户是否有有效订阅或处于试用期
  const hasValidAccess = () => {
    // 加载中时不进行判断
    if (isLoading) return true;
    
    // 如果是管理员，始终可以访问
    if (user?.role === 'admin') return true;
    
    // 检查是否有有效订阅或在试用期内
    return subscription?.status === 'active' || subscription?.status === 'trial';
  };

  // 检查并更新用户状态
  useEffect(() => {
    const checkAndUpdateUserStatus = async () => {
      if (!user) return;

      try {
        setIsLoading(true);

        // 获取最新的订阅信息
        const response = await getSubscriptionInfo();
        setSubscription(response.subscription);

        // 检查用户角色是否与实际状态匹配
        const currentStatus = response.subscription.status; // 'active', 'trial', 'inactive'
        let expectedRole: string;

        if (currentStatus === 'active') {
          expectedRole = 'subscriber';
        } else if (currentStatus === 'trial') {
          expectedRole = 'trial';
        } else {
          expectedRole = 'normal';
        }

        // 如果用户角色与实际状态不符，重新获取用户信息
        if (user.role !== expectedRole) {
          console.log(`[Dashboard] 检测到用户状态变化: ${user.role} -> ${expectedRole}，更新用户信息`);
          await fetchUserInfo();
        }

      } catch (error) {
        console.error('检查用户状态失败:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkAndUpdateUserStatus();
  }, [user, fetchUserInfo]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-cyber-bg via-cyber-bg to-cyber-card relative overflow-hidden">
      {/* 页面主体内容 */}
      <main className="container mx-auto px-4 pt-2 pb-4 relative z-10">
        {/* 图表区域 */}
        <div className="relative">
          <MainKlineChart
            showLoadingIndicator={false}
            symbol="BTC/USDT"
          />

          {/* 订阅遮罩 - 当没有有效访问权限时显示 */}
          {!hasValidAccess() && (
            <div className="absolute inset-0 bg-cyber-bg/90 backdrop-blur-sm flex flex-col items-center justify-center z-30">
              <div className="w-full max-w-md bg-cyber-card/40 backdrop-blur-xl shadow-2xl border border-cyber-border/50 rounded-2xl p-8 text-center relative overflow-hidden">
                {/* Card glow effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-cyber-cyan/5 via-transparent to-cyber-purple/5 rounded-2xl"></div>
                <div className="absolute -inset-1 bg-gradient-to-br from-cyber-cyan/20 via-transparent to-cyber-purple/20 rounded-2xl blur-sm opacity-30"></div>

                <div className="relative z-10">
                  <h2 className="text-2xl font-bold mb-4 text-cyber-text font-sans">{t('dashboard.subscribeToAccess')}</h2>
                  <p className="text-cyber-muted mb-4 font-mono">
                    {subscription?.status === 'inactive' ? t('dashboard.trialEnded') : t('dashboard.accountNotSubscribed')}。
                    {t('dashboard.subscribeForFullAccess')}
                  </p>
                  <Button
                    className="bg-gradient-to-r from-cyber-cyan to-cyber-blue hover:from-cyber-cyan/90 hover:to-cyber-blue/90 text-black font-mono transition-all duration-200"
                    onClick={() => navigate('/subscribe')}
                  >
                    {t('subscription.subscribeNow')}
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default Dashboard; 