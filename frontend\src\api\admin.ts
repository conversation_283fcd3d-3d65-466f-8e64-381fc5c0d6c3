import axios from 'axios';

// 创建带有基础URL的axios实例
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:4000/api';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器，为请求添加token
api.interceptors.request.use(
  (config) => {
    const adminStorage = localStorage.getItem('admin-storage');

    if (adminStorage) {
      try {
        const parsedStorage = JSON.parse(adminStorage);
        const authToken = parsedStorage.state?.token;

        if (authToken) {
          config.headers.Authorization = `Bearer ${authToken}`;
        }
      } catch (error) {
        console.error('解析token时出错:', error);
        // 如果解析出错，清除可能损坏的存储
        localStorage.removeItem('admin-storage');
      }
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器，处理 token 失效的情况
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (axios.isAxiosError(error) && error.response?.status === 401) {
      // 如果响应包含错误码，提供更详细的日志
      const errorCode = error.response.data?.code;
      const errorMessage = error.response.data?.message || '认证失败';

      console.warn(`管理员API认证错误: ${errorCode || 'unknown'} - ${errorMessage}`);

      // 检查当前URL是否已经是管理员登录页
      const currentPath = window.location.pathname;

      // 如果已经在登录页或正在处理重定向，不再重定向
      if (currentPath === '/admin/login') {
        console.log('已在管理员登录页，不再重定向');
        return Promise.reject(error);
      }

      // token 失效，清除存储
      console.log('清除管理员认证信息并重定向到登录页');
      localStorage.removeItem('admin-storage');

      // 设置一个标志，表示正在执行重定向，避免循环
      sessionStorage.setItem('admin_redirecting', 'true');

      // 重定向到登录页
      window.location.href = '/admin/login';
    }
    return Promise.reject(error);
  }
);

// 管理员登录
export const adminLogin = async (email: string, password: string) => {
  try {
    const response = await api.post('/admin/login', {
      email,
      password
    });

    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '登录失败，请稍后重试');
    }
    throw error;
  }
};

// 获取用户列表
export const getUsers = async (page = 1, limit = 10, search = '', filters = {}) => {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...filters
    });

    if (search) {
      params.append('search', search);
    }

    const response = await api.get(`/admin/users?${params}`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '获取用户列表失败');
    }
    throw error;
  }
};

// 更新用户角色
export const updateUserRole = async (userId: string, role: string) => {
  try {
    const response = await api.patch(`/admin/users/${userId}`, { role });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '更新用户角色失败');
    }
    throw error;
  }
};

// 创建用户
export const createUser = async (userData: { email: string; password: string; role?: string; isVerified?: boolean }) => {
  try {
    const response = await api.post('/admin/users', userData);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '创建用户失败');
    }
    throw error;
  }
};

// 删除用户
export const deleteUser = async (userId: string) => {
  try {
    const response = await api.delete(`/admin/users/${userId}`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '删除用户失败');
    }
    throw error;
  }
};

// 封禁用户
export const banUser = async (userId: string) => {
  try {
    const response = await api.post(`/admin/users/${userId}/ban`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '封禁用户失败');
    }
    throw error;
  }
};

// 解封用户
export const unbanUser = async (userId: string) => {
  try {
    const response = await api.post(`/admin/users/${userId}/unban`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '解封用户失败');
    }
    throw error;
  }
};

// 获取系统设置
export const getSystemSettings = async () => {
  try {
    const response = await api.get('/admin/settings');
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '获取系统设置失败');
    }
    throw error;
  }
};

// 更新系统设置
export const updateSystemSettings = async (settings: any) => {
  try {
    const response = await api.put('/admin/settings', settings);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '更新系统设置失败');
    }
    throw error;
  }
};

// 获取支付记录
export const getPayments = async (page = 1, limit = 10, search = '', filters = {}) => {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...filters
    });

    if (search) {
      params.append('search', search);
    }

    const response = await api.get(`/admin/payments?${params}`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '获取支付记录失败');
    }
    throw error;
  }
};

// 获取支付统计数据
export const getPaymentStats = async () => {
  try {
    const response = await api.get('/admin/payments/stats');
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '获取支付统计数据失败');
    }
    throw error;
  }
};

// 获取支付详情
export const getPaymentDetails = async (paymentId: string) => {
  try {
    const response = await api.get(`/admin/payments/${paymentId}`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '获取支付详情失败');
    }
    throw error;
  }
};

// 更新支付状态
export const updatePaymentStatus = async (paymentId: string, paymentStatus: string) => {
  try {
    const response = await api.patch(`/admin/payments/${paymentId}`, { paymentStatus });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '更新支付状态失败');
    }
    throw error;
  }
};



// 更新用户订阅
export const updateUserSubscription = async (userId: string, subscriptionData: any) => {
  try {
    const response = await api.patch(`/admin/users/${userId}/subscription`, subscriptionData);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.message || '更新用户订阅失败');
    }
    throw error;
  }
};

// 获取管理员认证头
export const getAdminAuthHeader = () => {
  const adminStorage = localStorage.getItem('admin-storage');

  if (adminStorage) {
    try {
      const parsedStorage = JSON.parse(adminStorage);
      const authToken = parsedStorage.state?.token;

      if (authToken) {
        return { Authorization: `Bearer ${authToken}` };
      }
    } catch (error) {
      console.error('解析token时出错:', error);
    }
  }

  return {};
};

// 导出axios实例供直接使用
export { api as adminApiInstance };

export default {
  adminLogin,
  getUsers,
  updateUserRole,
  createUser,
  deleteUser,
  banUser,
  unbanUser,
  getSystemSettings,
  updateSystemSettings,
  getPayments,
  getPaymentStats,
  getPaymentDetails,
  updatePaymentStatus,
  updateUserSubscription,
  getAdminAuthHeader
};