// 测试调度器和预测服务的验证逻辑差异

// 调度器的逻辑
function schedulerShouldTrigger(hour, minute) {
  const isOddHour = hour % 2 === 1;
  return isOddHour
    ? [2, 28, 58].includes(minute)
    : [28].includes(minute);
}

// 预测服务的逻辑
function predictionServiceIsValid(hour, minute) {
  const isOddHour = hour % 2 === 1;
  const paddedMinute = minute.toString().padStart(2, '0');
  
  if (isOddHour) {
    return ['02', '28', '58'].includes(paddedMinute);
  } else {
    return ['28'].includes(paddedMinute);
  }
}

// 测试所有可能的触发时间
const testCases = [
  { hour: 19, minute: 2, desc: '19:02 (奇数小时02分)' },
  { hour: 19, minute: 28, desc: '19:28 (奇数小时28分)' },
  { hour: 19, minute: 58, desc: '19:58 (奇数小时58分)' },
  { hour: 20, minute: 28, desc: '20:28 (偶数小时28分)' },
  { hour: 1, minute: 2, desc: '01:02 (奇数小时02分)' },
  { hour: 1, minute: 28, desc: '01:28 (奇数小时28分)' },
  { hour: 1, minute: 58, desc: '01:58 (奇数小时58分)' },
];

console.log('验证逻辑一致性测试：\n');

let hasInconsistency = false;

testCases.forEach(testCase => {
  const schedulerResult = schedulerShouldTrigger(testCase.hour, testCase.minute);
  const predictionResult = predictionServiceIsValid(testCase.hour, testCase.minute);
  
  const consistent = schedulerResult === predictionResult;
  if (!consistent) hasInconsistency = true;
  
  console.log(`${testCase.desc}:`);
  console.log(`  调度器判断: ${schedulerResult}`);
  console.log(`  预测服务判断: ${predictionResult}`);
  console.log(`  一致性: ${consistent ? '✅' : '❌'}`);
  console.log('');
});

console.log(`总体一致性: ${hasInconsistency ? '❌ 存在不一致' : '✅ 完全一致'}`);

// 特别测试边界情况
console.log('\n边界情况测试：');

// 测试分钟数为个位数的情况
const edgeCases = [
  { hour: 19, minute: 2 },
  { hour: 19, minute: 8 },
  { hour: 19, minute: 9 },
];

edgeCases.forEach(testCase => {
  const schedulerResult = schedulerShouldTrigger(testCase.hour, testCase.minute);
  const predictionResult = predictionServiceIsValid(testCase.hour, testCase.minute);
  
  console.log(`${testCase.hour}:${testCase.minute.toString().padStart(2, '0')}:`);
  console.log(`  调度器: ${schedulerResult}, 预测服务: ${predictionResult}`);
});
