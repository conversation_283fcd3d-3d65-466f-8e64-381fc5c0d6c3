// 测试cron任务的时间冲突
const cron = require('node-cron');

console.log('开始测试cron任务时间冲突...\n');

// 模拟预测任务 (每分钟)
cron.schedule('* * * * *', () => {
  const now = new Date();
  const minute = now.getUTCMinutes();
  const hour = now.getUTCHours();
  const isOddHour = hour % 2 === 1;
  
  const shouldTrigger = isOddHour
    ? [2, 28, 58].includes(minute)
    : [28].includes(minute);
    
  if (shouldTrigger) {
    console.log(`[预测任务] ${now.toISOString()} - 触发预测任务，小时: ${hour}, 分钟: ${minute}`);
  }
});

// 模拟K线刷新任务 (1-2分和31-32分)
cron.schedule('1-2,31-32 * * * *', () => {
  const now = new Date();
  console.log(`[K线刷新] ${now.toISOString()} - 执行K线刷新任务`);
});

// 模拟节气预测任务 (每小时整点)
cron.schedule('0 * * * *', () => {
  const now = new Date();
  console.log(`[节气预测] ${now.toISOString()} - 检查节气预测任务`);
});

// 模拟用户角色更新 (每小时整点)
cron.schedule('0 * * * *', () => {
  const now = new Date();
  console.log(`[用户角色] ${now.toISOString()} - 执行用户角色更新任务`);
});

// 特别关注58分和00分的时间点
cron.schedule('58,0 * * * *', () => {
  const now = new Date();
  const minute = now.getUTCMinutes();
  console.log(`[时间监控] ${now.toISOString()} - 关键时间点: ${minute}分`);
});

console.log('cron任务已启动，观察时间冲突情况...');
console.log('特别关注58分和00分的执行情况');

// 运行5分钟后停止
setTimeout(() => {
  console.log('\n测试完成');
  process.exit(0);
}, 5 * 60 * 1000);
