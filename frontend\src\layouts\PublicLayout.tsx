import React, { useEffect, useState } from 'react';
import { Link, Outlet } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Button } from "../components/ui/button";

import configService, { SiteInfo } from '../services/configService';
import GeometricLogo from '../components/GeometricLogo';
import CyberLanguageSwitcher from '../components/CyberLanguageSwitcher';
import CyberFooter from '../components/CyberFooter';



const PublicLayout: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [siteInfo, setSiteInfo] = useState<SiteInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 加载网站配置
  useEffect(() => {
    const loadSiteInfo = async () => {
      try {
        const config = await configService.getConfig();
        setSiteInfo(config.siteInfo);

        // 语言设置已在 App.tsx 中处理，这里只需要加载配置
      } catch (error) {
        console.error('加载网站配置失败:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadSiteInfo();
  }, []); // 移除 i18n 依赖，避免语言切换时重复执行

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
    // 标记为用户主动选择的语言
    localStorage.setItem('userChosenLanguage', 'true');
  };

  return (
    <div className="min-h-screen bg-cyber-bg flex flex-col">
      {/* 网格背景 */}
      <div className="fixed inset-0 bg-[linear-gradient(rgba(0,245,255,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(0,245,255,0.03)_1px,transparent_1px)] bg-[size:50px_50px] pointer-events-none" />

      {/* 顶部导航栏 */}
      <header className="relative z-10 h-16 bg-cyber-card/20 backdrop-blur-md border-b border-cyber-border/50">
        {/* 动画边框 */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-cyber-cyan to-transparent animate-pulse-neon" />

        <div className="container mx-auto px-4 h-full">
          <div className="flex items-center justify-between h-full">
            {/* Left side: Logo + Navigation Menu */}
            <div className="flex items-center space-x-8">
              {/* Logo */}
              <GeometricLogo
                to="/"
                siteName={siteInfo?.siteName || t('app.title')}
                size="xl"
              />

              {/* 导航链接 */}
              <nav className="hidden md:flex items-center space-x-6">
                <Link
                  to="/docs"
                  className="text-cyber-muted hover:text-cyber-cyan transition-colors duration-200 font-mono text-sm tracking-wide px-3 py-2 rounded-lg hover:bg-cyber-border/10 outline-none focus:outline-none"
                >
                  {t('nav.docs')}
                </Link>
                <Link
                  to="/faq"
                  className="text-cyber-muted hover:text-cyber-cyan transition-colors duration-200 font-mono text-sm tracking-wide px-3 py-2 rounded-lg hover:bg-cyber-border/10 outline-none focus:outline-none"
                >
                  {t('nav.faq')}
                </Link>
                <Link
                  to="/announcement"
                  className="text-cyber-muted hover:text-cyber-cyan transition-colors duration-200 font-mono text-sm tracking-wide px-3 py-2 rounded-lg hover:bg-cyber-border/10 outline-none focus:outline-none"
                >
                  {t('nav.announcement')}
                </Link>
              </nav>
            </div>

            {/* Right side: Language Switcher + Login/Register Buttons */}
            <div className="flex items-center space-x-4">
              {/* 语言切换 */}
              <CyberLanguageSwitcher />

              {/* 登录注册按钮 */}
              <Button asChild variant="ghost" className="text-cyber-text hover:text-cyber-cyan border border-cyber-border hover:border-cyber-cyan/30 bg-cyber-card/30 hover:bg-cyber-card/40 backdrop-blur-sm transition-all duration-300 px-4 py-2 h-10 font-mono text-sm outline-none focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-cyber-cyan/30">
                <Link to="/login">
                  {t('auth.signIn')}
                </Link>
              </Button>
              <Button asChild className="bg-gradient-to-r from-cyber-cyan to-cyber-blue hover:from-cyber-cyan/90 hover:to-cyber-blue/90 text-black font-bold px-4 py-2 h-10 rounded-lg shadow-lg hover:shadow-[0_0_10px_rgba(0,245,255,0.2)] transition-all duration-300 font-mono text-sm outline-none focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0">
                <Link to="/register">
                  {t('auth.signUp')}
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* 主内容区域 */}
      <main className="relative z-10 flex-1">
        <Outlet />
      </main>

      {/* 页脚 */}
      <CyberFooter theme="cyber" className="relative z-10" />
    </div>
  );
};

export default PublicLayout;