import express from 'express';
import documentController from '../controllers/documentController';
import authenticate, { isAdmin } from '../middlewares/authMiddleware';

const router = express.Router();

// 所有管理员文档API需要先经过认证和权限验证
router.use(authenticate);
router.use(isAdmin);

// 获取管理员文档列表（包含非公开文档）
// GET /api/admin/docs
router.get('/', documentController.getAdminDocuments);

// 获取文档详情（管理员可查看所有文档）
// GET /api/admin/docs/:id
router.get('/:id', documentController.getDocumentById);

// 创建文档
// POST /api/admin/docs
router.post('/', documentController.createDocument);

// 更新文档
// PUT /api/admin/docs/:id
router.put('/:id', documentController.updateDocument);

// 删除文档
// DELETE /api/admin/docs/:id
router.delete('/:id', documentController.deleteDocument);

// 翻译文档
// POST /api/admin/docs/:id/translate
router.post('/:id/translate', documentController.translateDocument);

export default router;
