// 测试时间计算差异
function calculateTimesOriginal(currentTime) {
  const hour = currentTime.getUTCHours();
  const minute = currentTime.getUTCMinutes();
  const isOddHour = hour % 2 === 1;

  let sourceKlineTime;
  let targetStartTime;
  let targetEndTime;

  let workDate = new Date(currentTime);
  workDate.setUTCSeconds(0);
  workDate.setUTCMilliseconds(0);

  if (isOddHour) {
    if (minute === 58) {
      workDate.setUTCMinutes(2);
      sourceKlineTime = workDate.getTime();
      workDate.setUTCHours(hour + 1);
      workDate.setUTCMinutes(0);
      targetStartTime = workDate.getTime();
      workDate.setUTCMinutes(30);
      targetEndTime = workDate.getTime();
    }
  }

  return { sourceKlineTime, targetStartTime, targetEndTime };
}

function calculateTimesFixed(currentTime) {
  const hour = currentTime.getUTCHours();
  const minute = currentTime.getUTCMinutes();
  const isOddHour = hour % 2 === 1;

  let sourceKlineTime;
  let targetStartTime;
  let targetEndTime;

  if (isOddHour) {
    if (minute === 58) {
      let workDate = new Date(currentTime);
      workDate.setUTCSeconds(0);
      workDate.setUTCMilliseconds(0);
      workDate.setUTCMinutes(2);
      sourceKlineTime = workDate.getTime();
      
      workDate = new Date(currentTime);
      workDate.setUTCSeconds(0);
      workDate.setUTCMilliseconds(0);
      workDate.setUTCHours(hour + 1);
      workDate.setUTCMinutes(0);
      targetStartTime = workDate.getTime();
      
      workDate = new Date(currentTime);
      workDate.setUTCSeconds(0);
      workDate.setUTCMilliseconds(0);
      workDate.setUTCHours(hour + 1);
      workDate.setUTCMinutes(30);
      targetEndTime = workDate.getTime();
    }
  }

  return { sourceKlineTime, targetStartTime, targetEndTime };
}

// 测试19:58的情况
const testTime = new Date('2025-07-23T19:58:00.000Z');
console.log('测试时间:', testTime.toISOString());

const original = calculateTimesOriginal(testTime);
const fixed = calculateTimesFixed(testTime);

console.log('\n原始方法结果:');
console.log('源K线时间:', new Date(original.sourceKlineTime).toISOString());
console.log('目标开始时间:', new Date(original.targetStartTime).toISOString());
console.log('目标结束时间:', new Date(original.targetEndTime).toISOString());

console.log('\n修复方法结果:');
console.log('源K线时间:', new Date(fixed.sourceKlineTime).toISOString());
console.log('目标开始时间:', new Date(fixed.targetStartTime).toISOString());
console.log('目标结束时间:', new Date(fixed.targetEndTime).toISOString());

console.log('\n时间戳差异:');
console.log('源K线时间差异:', original.sourceKlineTime - fixed.sourceKlineTime);
console.log('目标开始时间差异:', original.targetStartTime - fixed.targetStartTime);
console.log('目标结束时间差异:', original.targetEndTime - fixed.targetEndTime);

console.log('\n是否完全相同:', 
  original.sourceKlineTime === fixed.sourceKlineTime &&
  original.targetStartTime === fixed.targetStartTime &&
  original.targetEndTime === fixed.targetEndTime
);
