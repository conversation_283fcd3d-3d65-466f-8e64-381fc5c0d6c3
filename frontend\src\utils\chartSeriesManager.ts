/**
 * 图表系列管理器
 * 
 * 负责创建和管理图表实例及其所有系列
 * 提供统一的图表切换、样式管理、实例访问接口
 */

import { createChart, IChartApi, ISeriesApi, DeepPartial, ChartOptions, TickMarkType } from 'lightweight-charts';
import { getDefaultChartOptions } from './chartUtils';
import { getMainSeriesOptions } from './chartUtils';
import { getPredictionSeriesOptions, getPredictionPriceScaleOptions } from './chartUtils';
import { getRealtimeLineOptions } from './chartUtils';
import { getSolarPredictionLineOptions } from './chartUtils';
import timezoneManager, { TimezoneOption } from './timezoneManager';

// 图表类型定义
export type ChartType = 'candlestick' | 'line';

// 图表系列接口
export interface ChartSeries {
  candlestick: ISeriesApi<'Candlestick'> | null;
  predictionCandlestick: ISeriesApi<'Candlestick'> | null;
  realtimeLine: ISeriesApi<'Line'> | null;
  solarPrediction: ISeriesApi<'Area'> | null;
}

// 图表管理器类
export class ChartSeriesManager {
  private chartInstance: IChartApi | null = null;
  private chartSeries: ChartSeries = {
    candlestick: null,
    predictionCandlestick: null,
    realtimeLine: null,
    solarPrediction: null,
  };
  private container: HTMLDivElement | null = null;
  private currentChartType: ChartType = 'candlestick';

  constructor() {
    // 监听时区变更事件
    timezoneManager.addListener(this.handleTimezoneChange.bind(this));
  }

  /**
   * 处理时区变更
   * @param timezone 新的时区信息
   */
  private handleTimezoneChange(timezone: TimezoneOption): void {
    // 当时区变更时，需要刷新图表时间刻度
    if (this.chartInstance) {
      console.log(`时区已切换到: ${timezone.label}`);
      
      // 刷新图表以反映新的时区，但保持视图状态
      this.refreshTimezone();
      
      // 刷新价格提示框格式
      this.chartInstance.applyOptions({
        crosshair: {
          vertLine: {
            labelBackgroundColor: this.chartInstance.options().crosshair?.vertLine?.labelBackgroundColor || '#758696',
          }
        }
      });
    }
  }

  /**
   * 初始化图表实例
   * @param container 图表容器DOM元素
   * @param width 图表宽度
   * @param height 图表高度
   * @param watermarkText 水印文本（可选）
   * @returns 创建的图表实例
   */
  public initChart(container: HTMLDivElement, width: number, height: number, watermarkText?: string): IChartApi {
    // 如果图表实例已存在，更新水印并返回
    if (this.chartInstance) {
      if (watermarkText) {
        this.updateWatermark(watermarkText);
      }
      return this.chartInstance;
    }

    this.container = container;
    const options = getDefaultChartOptions(width, watermarkText);
    options.height = height;

    // 使用默认选项中的本地化配置，不再需要额外添加

    this.chartInstance = createChart(container, options);
    console.log('图表实例已创建', watermarkText ? `，水印: ${watermarkText}` : '');

    // 初始化所有系列
    this.initAllSeries();

    return this.chartInstance;
  }

  /**
   * 初始化所有图表系列
   */
  private initAllSeries(): void {
    if (!this.chartInstance) {
      return;
    }
    
    // 创建K线系列
    this.chartSeries.candlestick = this.chartInstance.addCandlestickSeries(
      getMainSeriesOptions()
    );

    // 创建预测K线系列
    this.chartSeries.predictionCandlestick = this.chartInstance.addCandlestickSeries(
      getPredictionSeriesOptions()
    );
    
    // 设置预测价格坐标轴
    this.chartInstance.priceScale('prediction').applyOptions(
      getPredictionPriceScaleOptions()
    );
    
    // 添加中线到预测区域
    this.chartSeries.predictionCandlestick.createPriceLine({
      price: 0,
      color: 'rgba(255, 255, 255, 0.5)',
      lineWidth: 1,
      lineStyle: 1,
      axisLabelVisible: false,
      title: '',
    });

    // 创建实时折线系列
    this.chartSeries.realtimeLine = this.chartInstance.addLineSeries(
      getRealtimeLineOptions()
    );
    
    // 创建节气预测折线系列
    this.chartSeries.solarPrediction = this.chartInstance.addAreaSeries(
      getSolarPredictionLineOptions()
    );
    
    // 设置节气预测价格坐标轴 - 调整位置和可见性以增强显示效果
    this.chartInstance.priceScale('prediction-line').applyOptions({
      scaleMargins: {
        top: 0.6, // 从0.6改为0.5，更靠近中间位置
        bottom: 0.0,
      },
      visible: true,
      borderVisible: true, // 添加边框以增强可见性
      borderColor: '#8B5FBF', // 边框颜色与节气预测线保持一致
      autoScale: true,
      entireTextOnly: true, // 确保标签完整显示
      mode: 0,
      alignLabels: true,
    });

    console.log('所有图表系列已初始化');
    
    // 默认显示K线图模式
    this.switchChartType('candlestick');
  }

  /**
   * 切换图表类型
   * @param type 图表类型 'candlestick' 或 'line'
   */
  public switchChartType(type: ChartType): void {
    if (!this.chartInstance || this.currentChartType === type) {
      return;
    }

    this.currentChartType = type;

    // 成对切换主图与副图
    if (type === 'candlestick') {
      // 显示K线图和预测K线
      this.chartSeries.candlestick?.applyOptions({ visible: true });
      this.chartSeries.predictionCandlestick?.applyOptions({ visible: true });
      
      // 隐藏折线图和预测折线
      this.chartSeries.realtimeLine?.applyOptions({ visible: false });
      this.chartSeries.solarPrediction?.applyOptions({ visible: false });
      
      // 调整坐标轴可见性
      this.chartInstance.priceScale('prediction-line').applyOptions({ visible: false });
      this.chartInstance.priceScale('prediction').applyOptions({ visible: true });
      
      console.log('已切换到K线图模式');
    } else {
      // 显示折线图和预测折线
      this.chartSeries.realtimeLine?.applyOptions({ visible: true });
      this.chartSeries.solarPrediction?.applyOptions({ visible: true });
      
      // 隐藏K线图和预测K线
      this.chartSeries.candlestick?.applyOptions({ visible: false });
      this.chartSeries.predictionCandlestick?.applyOptions({ visible: false });
      
      // 调整坐标轴可见性
      this.chartInstance.priceScale('prediction').applyOptions({ visible: false });
      this.chartInstance.priceScale('prediction-line').applyOptions({ visible: true });
      
      console.log('已切换到折线图模式');
    }
  }

  /**
   * 调整图表大小
   * @param width 新宽度
   * @param height 新高度（可选）
   */
  public resize(width: number, height?: number): void {
    if (!this.chartInstance) {
      return;
    }

    const options: DeepPartial<ChartOptions> = { width };
    if (height) {
      options.height = height;
    }

    // 应用新的尺寸配置到图表实例
    this.chartInstance.applyOptions(options);
  }

  /**
   * 获取图表实例
   * @returns 图表实例
   */
  public getChartInstance(): IChartApi | null {
    return this.chartInstance;
  }

  /**
   * 获取图表系列
   * @returns 所有图表系列
   */
  public getSeries(): ChartSeries {
    return this.chartSeries;
  }

  /**
   * 显示或隐藏预测数据
   * @param visible 是否可见
   * @param isPredictionLine 是否为预测折线（默认为K线预测）
   */
  public togglePredictionVisibility(visible: boolean, isPredictionLine: boolean = false): void {
    if (!this.chartInstance) {
      return;
    }

    if (isPredictionLine) {
      this.chartSeries.solarPrediction?.applyOptions({ visible });
    } else {
      this.chartSeries.predictionCandlestick?.applyOptions({ visible });
    }
  }

  /**
   * 刷新图表时区
   * 在时区变更后调用，重绘图表以应用新的时区设置
   */
  public refreshTimezone(): void {
    if (!this.chartInstance) return;
    
    // 保存当前视图状态
    const timeScale = this.chartInstance.timeScale();
    const currentVisibleLogicalRange = timeScale.getVisibleLogicalRange();
    
    // 更新本地化配置
    this.chartInstance.applyOptions({
      localization: {
        // 使用统一的时间格式 yyyy-MM-dd HH:mm
        timeFormatter: (timestamp: number): string => {
          const date = timezoneManager.convertToCurrentTimezone(timestamp);
          const year = date.getFullYear();
          const month = (date.getMonth() + 1).toString().padStart(2, '0');
          const day = date.getDate().toString().padStart(2, '0');
          const hours = date.getHours().toString().padStart(2, '0');
          const minutes = date.getMinutes().toString().padStart(2, '0');
          
          return `${year}-${month}-${day} ${hours}:${minutes}`;
        },
        dateFormat: 'yyyy-MM-dd',
      }
    });
    
    // 轻微触发图表重绘，但不改变视图范围
    if (currentVisibleLogicalRange) {
      // 使用微小的延迟以确保更改已应用
      setTimeout(() => {
        // 恢复原有的视图范围
        timeScale.setVisibleLogicalRange(currentVisibleLogicalRange);
      }, 5);
    }
  }

  /**
   * 更新水印
   * @param watermarkText 水印文本
   */
  public updateWatermark(watermarkText: string): void {
    if (!this.chartInstance) {
      return;
    }

    const watermarkOptions = watermarkText ? {
      visible: true,
      text: watermarkText,
      fontSize: 88,                              // 调整字体大小
      color: 'rgba(255, 255, 255, 0.15)',       // 调整颜色和透明度
      horzAlign: 'center' as const,              // 水平对齐
      vertAlign: 'center' as const,              // 垂直对齐
    } : { visible: false };

    this.chartInstance.applyOptions({
      watermark: watermarkOptions
    });

    console.log('水印已更新:', watermarkText);
  }

  /**
   * 清理和销毁图表实例
   */
  public destroy(): void {
    if (this.chartInstance) {
      // 移除时区变更监听器
      timezoneManager.removeListener(this.handleTimezoneChange.bind(this));

      this.chartInstance.remove();
      this.chartInstance = null;
      this.chartSeries = {
        candlestick: null,
        predictionCandlestick: null,
        realtimeLine: null,
        solarPrediction: null,
      };
      console.log('图表实例已销毁');
    }
  }
}

// 创建单例实例
export const chartManager = new ChartSeriesManager();

// 默认导出单例
export default chartManager; 