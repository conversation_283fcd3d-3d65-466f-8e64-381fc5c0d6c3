import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { BarChart2 } from 'lucide-react';
import configService from '../services/configService';

export const Logo: React.FC = () => {
  const [siteName, setSiteName] = useState('BTC 预测');

  useEffect(() => {
    const loadSiteName = async () => {
      try {
        const name = await configService.getSiteName();
        setSiteName(name);
      } catch (error) {
        console.error('获取网站名称失败:', error);
      }
    };
    loadSiteName();
  }, []);

  return (
    <Link to="/" className="flex items-center space-x-2">
      <BarChart2 className="h-8 w-8 text-primary" />
      <span className="text-xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
        {siteName}
      </span>
    </Link>
  );
};