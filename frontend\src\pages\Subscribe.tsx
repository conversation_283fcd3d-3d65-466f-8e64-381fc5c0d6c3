import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Check, Zap, Clock } from 'lucide-react';
import { cn } from '../lib/utils';
import useUserStore from '../store/useUserStore';
import { createSubscription, getSubscriptionInfo } from '../api/subscription';
import { toast } from '../components/ui/use-toast';
import { getPublicSettings } from '../api/public';

// 默认订阅计划数据（作为备用）
const defaultSubscriptionPlans = [
  {
    id: 'monthly',
    name: '月度订阅',
    price: '$39',
    duration: '30天',
    billingCycle: '每月',
    features: [
      '完整访问所有图表分析',
      '实时市场数据'
    ],
    popular: false,
    color: 'primary'
  },
  {
    id: 'quarterly',
    name: '季度订阅',
    price: '$87',
    duration: '90天',
    billingCycle: '每季度',
    features: [
      '完整访问所有图表分析',
      '实时市场数据',
      '每季度节省 $30'
    ],
    popular: true,
    color: 'success'
  },
  {
    id: 'yearly',
    name: '年度订阅',
    price: '$299',
    duration: '365天',
    billingCycle: '每年',
    features: [
      '完整访问所有图表分析',
      '实时市场数据',
      '每年节省 $169'
    ],
    popular: false,
    color: 'primary'
  }
];

const Subscribe: React.FC = () => {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState<string | null>(null);
  const [subscriptionPlans, setSubscriptionPlans] = useState(defaultSubscriptionPlans);
  const { user, fetchUserInfo } = useUserStore();

  // 生成本地化的订阅计划数据
  const getLocalizedPlans = () => [
    {
      id: 'monthly',
      name: t('subscription.monthly'),
      price: subscriptionPlans.find(p => p.id === 'monthly')?.price || '$39',
      duration: `30${t('subscription.durationDays')}`,
      billingCycle: t('subscription.perMonth'),
      features: [
        t('subscription.fullAccess'),
        t('subscription.realTimeData')
      ],
      popular: false,
      color: 'primary'
    },
    {
      id: 'quarterly',
      name: t('subscription.quarterly'),
      price: subscriptionPlans.find(p => p.id === 'quarterly')?.price || '$87',
      duration: `90${t('subscription.durationDays')}`,
      billingCycle: t('subscription.perQuarter'),
      features: [
        t('subscription.fullAccess'),
        t('subscription.realTimeData'),
        t('subscription.quarterlySave')
      ],
      popular: true,
      color: 'success'
    },
    {
      id: 'yearly',
      name: t('subscription.yearly'),
      price: subscriptionPlans.find(p => p.id === 'yearly')?.price || '$299',
      duration: `365${t('subscription.durationDays')}`,
      billingCycle: t('subscription.perYear'),
      features: [
        t('subscription.fullAccess'),
        t('subscription.realTimeData'),
        t('subscription.yearlySave')
      ],
      popular: false,
      color: 'warning'
    }
  ];

  // 检查并更新用户状态
  useEffect(() => {
    const checkUserStatus = async () => {
      if (!user) return;

      try {
        // 获取最新的订阅信息来检查状态
        const response = await getSubscriptionInfo();
        const currentStatus = response.subscription.status;

        let expectedRole: string;
        if (currentStatus === 'active') {
          expectedRole = 'subscriber';
        } else if (currentStatus === 'trial') {
          expectedRole = 'trial';
        } else {
          expectedRole = 'normal';
        }

        // 如果用户角色与实际状态不符，重新获取用户信息
        if (user.role !== expectedRole) {
          console.log(`[Subscribe] 检测到用户状态变化: ${user.role} -> ${expectedRole}，更新用户信息`);
          await fetchUserInfo();
        }
      } catch (error) {
        console.error('[Subscribe] 检查用户状态失败:', error);
      }
    };

    checkUserStatus();
  }, [user, fetchUserInfo]);

  // 从后端获取订阅价格（静默更新，不显示加载状态）
  useEffect(() => {
    const fetchPrices = async () => {
      try {
        const response = await getPublicSettings();

        if (response.settings.subscriptionPrices) {
          const prices = response.settings.subscriptionPrices;

          // 更新订阅计划价格
          const updatedPlans = defaultSubscriptionPlans.map(plan => {
            if (plan.id === 'monthly' && prices.monthly) {
              return { ...plan, price: `$${prices.monthly}` };
            } else if (plan.id === 'quarterly' && prices.quarterly) {
              return { ...plan, price: `$${prices.quarterly}` };
            } else if (plan.id === 'yearly' && prices.yearly) {
              return { ...plan, price: `$${prices.yearly}` };
            }
            return plan;
          });

          setSubscriptionPlans(updatedPlans);
        }
      } catch (error) {
        console.error('获取订阅价格失败', error);
        // 静默失败，继续使用默认价格
      }
    };

    fetchPrices();
  }, []);

  // 处理订阅
  const handleSubscribe = async (planId: string) => {
    try {
      setIsLoading(planId);

      // 创建订阅
      const response = await createSubscription(planId);

      // 跳转到支付页面
      window.location.href = response.payment.invoiceUrl;

    } catch (error) {
      console.error('订阅创建失败', error);
      toast({
        title: t('subscription.subscriptionFailed'),
        description: t('subscription.subscriptionFailedDesc'),
        variant: 'destructive'
      });
    } finally {
      setIsLoading(null);
    }
  };

  return (
    <div className="w-full h-full bg-gradient-to-br from-cyber-bg via-cyber-bg to-cyber-card relative overflow-hidden">
      <div className="container mx-auto py-12 px-4 relative z-10 h-full">
        <div className="text-center mb-12">
          <h1 className="text-4xl text-cyber-text font-bold mb-4 font-sans">{t('subscription.chooseYourPlan')}</h1>
          <p className="text-cyber-muted max-w-2xl mx-auto font-mono">
            {t('subscription.subscriptionDesc')}
          </p>

          {user?.role === 'trial' && (
            <div className="mt-6 p-4 bg-cyber-cyan/10 text-cyber-cyan border border-cyber-cyan/30 rounded-xl inline-block backdrop-blur-sm">
              <div className="flex items-center space-x-2 font-mono">
                <Clock className="h-4 w-4" />
                <span>{t('subscription.trialEndsAt', { date: new Date(user.trialEndsAt).toLocaleDateString() })}</span>
              </div>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {getLocalizedPlans().map((plan) => (
            <Card
              key={plan.id}
              className={cn(
                "flex flex-col h-full min-h-[420px] transition-all duration-300 relative overflow-hidden group",
                plan.popular
                  ? "bg-cyber-card/40 backdrop-blur-xl border border-cyber-cyan/50 shadow-2xl hover:shadow-[0_0_30px_rgba(0,245,255,0.4)]"
                  : "bg-cyber-card/35 backdrop-blur-xl border border-cyber-border/50 hover:border-cyber-cyan/60 hover:shadow-[0_0_20px_rgba(0,245,255,0.2)]"
              )}
            >
              {/* 卡片装饰效果 - 统一样式 */}
              <div className={cn(
                "absolute inset-0 rounded-2xl transition-opacity duration-300",
                plan.popular
                  ? "bg-gradient-to-br from-cyber-cyan/10 via-transparent to-cyber-purple/10"
                  : "bg-gradient-to-br from-cyber-cyan/5 via-transparent to-cyber-purple/5 group-hover:from-cyber-cyan/8 group-hover:to-cyber-purple/8"
              )}></div>
              <div className={cn(
                "absolute -inset-1 rounded-2xl blur-sm transition-opacity duration-300",
                plan.popular
                  ? "bg-gradient-to-br from-cyber-cyan/20 via-transparent to-cyber-purple/20 opacity-30"
                  : "bg-gradient-to-br from-cyber-cyan/10 via-transparent to-cyber-purple/10 opacity-0 group-hover:opacity-20"
              )}></div>

              <CardHeader className="relative z-10">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-xl text-cyber-text font-sans">{plan.name}</CardTitle>
                    <CardDescription className="text-cyber-muted font-mono">{plan.billingCycle}</CardDescription>
                  </div>
                  {plan.popular && (
                    <span className="px-3 py-1 rounded-full text-xs bg-cyber-cyan/20 text-cyber-cyan font-medium border border-cyber-cyan/30 font-mono">
                      {t('subscription.mostPopular')}
                    </span>
                  )}
                </div>
              </CardHeader>
              <CardContent className="flex-grow relative z-10">
                <div className="mb-6">
                  <span className="text-3xl font-bold text-cyber-text font-sans">{plan.price}</span>
                  <span className="text-cyber-muted ml-1 font-mono">/ {plan.duration}</span>
                </div>

                <ul className="space-y-3 mb-6">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <Check className={cn("h-5 w-5 mr-3 flex-shrink-0",
                        plan.popular ? "text-cyber-cyan" : "text-cyber-green")} />
                      <span className="text-cyber-muted font-mono text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              <CardFooter className="relative z-10">
                <Button
                  className={cn("w-full font-mono transition-all duration-200",
                    plan.popular
                      ? "bg-gradient-to-r from-cyber-cyan to-cyber-blue hover:from-cyber-cyan/90 hover:to-cyber-blue/90 text-black"
                      : "bg-gradient-to-r from-cyber-green to-cyber-cyan hover:from-cyber-green/90 hover:to-cyber-cyan/90 text-black"
                  )}
                  onClick={() => handleSubscribe(plan.id)}
                  disabled={!!isLoading}
                >
                  {isLoading === plan.id ? t('subscription.processing') : t('subscription.subscribeNow')}
                  {plan.popular && <Zap className="ml-2 h-4 w-4" />}
                </Button>
              </CardFooter>
            </Card>
          ))
        }
        </div>

        <div className="mt-12 text-center text-cyber-muted text-sm max-w-2xl mx-auto font-mono">
          <p>
            {t('subscription.subscriptionTerms')}
          </p>
        </div>
      </div>
    </div>
  );
};

export default Subscribe;