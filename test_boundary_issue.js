// 测试跨日边界问题

function calculateTimes(currentTime) {
  const hour = currentTime.getUTCHours();
  const minute = currentTime.getUTCMinutes();
  const isOddHour = hour % 2 === 1;

  let sourceKlineTime;
  let targetStartTime;
  let targetEndTime;

  let workDate = new Date(currentTime);
  workDate.setUTCSeconds(0);
  workDate.setUTCMilliseconds(0);

  if (isOddHour) {
    if (minute === 58) {
      workDate.setUTCMinutes(2);
      sourceKlineTime = workDate.getTime();
      workDate.setUTCHours(hour + 1);  // 这里可能出现问题！
      workDate.setUTCMinutes(0);
      targetStartTime = workDate.getTime();
      workDate.setUTCMinutes(30);
      targetEndTime = workDate.getTime();
    }
  }

  return { sourceKlineTime, targetStartTime, targetEndTime };
}

// 测试不同的58分时间点
const testCases = [
  new Date('2025-07-23T19:58:00.000Z'), // 19:58
  new Date('2025-07-23T21:58:00.000Z'), // 21:58
  new Date('2025-07-23T23:58:00.000Z'), // 23:58 - 跨日边界！
  new Date('2025-07-24T01:58:00.000Z'), // 01:58
];

console.log('测试跨日边界问题：\n');

testCases.forEach(testTime => {
  console.log(`测试时间: ${testTime.toISOString()}`);
  console.log(`UTC小时: ${testTime.getUTCHours()}, 分钟: ${testTime.getUTCMinutes()}`);
  
  try {
    const result = calculateTimes(testTime);
    
    console.log(`源K线时间: ${new Date(result.sourceKlineTime).toISOString()}`);
    console.log(`目标开始时间: ${new Date(result.targetStartTime).toISOString()}`);
    console.log(`目标结束时间: ${new Date(result.targetEndTime).toISOString()}`);
    
    // 检查时间是否合理
    const sourceDate = new Date(result.sourceKlineTime);
    const targetStartDate = new Date(result.targetStartTime);
    const targetEndDate = new Date(result.targetEndTime);
    
    console.log(`源K线小时: ${sourceDate.getUTCHours()}`);
    console.log(`目标开始小时: ${targetStartDate.getUTCHours()}`);
    console.log(`目标结束小时: ${targetEndDate.getUTCHours()}`);
    
    // 检查是否跨日
    const sourceDay = sourceDate.getUTCDate();
    const targetDay = targetStartDate.getUTCDate();
    if (sourceDay !== targetDay) {
      console.log('⚠️  检测到跨日情况！');
    }
    
  } catch (error) {
    console.log(`❌ 计算出错: ${error.message}`);
  }
  
  console.log('---');
});

// 特别测试23:58的情况
console.log('\n特别测试23:58的跨日情况：');
const testDate = new Date('2025-07-23T23:58:00.000Z');
console.log(`原始时间: ${testDate.toISOString()}`);

let workDate = new Date(testDate);
console.log(`工作日期初始: ${workDate.toISOString()}`);

workDate.setUTCMinutes(2);
console.log(`设置分钟为2: ${workDate.toISOString()}`);

workDate.setUTCHours(23 + 1); // hour + 1 = 24
console.log(`设置小时为24: ${workDate.toISOString()}`);
console.log(`实际小时值: ${workDate.getUTCHours()}`);
console.log(`实际日期: ${workDate.getUTCDate()}`);

workDate.setUTCMinutes(0);
console.log(`设置分钟为0: ${workDate.toISOString()}`);

workDate.setUTCMinutes(30);
console.log(`设置分钟为30: ${workDate.toISOString()}`);
